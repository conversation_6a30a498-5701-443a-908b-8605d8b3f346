import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Chip,
  useTheme,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  Divider
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import RefreshIcon from '@mui/icons-material/Refresh';
import ReplayIcon from '@mui/icons-material/Replay';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveGraphAsImage from '../components/SaveGraphAsImage';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '../components/Layout/Layout';
import AddQueueItemDialog from '../components/AddQueueItemDialog';
import EntityDoFollowToggle from '../components/EntityDoFollowToggle';
import apiService, { GraphData, GraphNode, Entity } from '../services/api';
import { useSnackbar } from 'notistack';
import ForceGraph2D from 'react-force-graph-2d';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`graph-tabpanel-${index}`}
      aria-labelledby={`graph-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 2 }}>{children}</Box>}
    </div>
  );
};

const GraphPage: React.FC = () => {
  const { id, type } = useParams();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();
  const graphRef = useRef<any>(null);
  const [loading, setLoading] = useState(false);
  const [graphData, setGraphData] = useState<GraphData>({ nodes: [], links: [] });
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [selectedEntity, setSelectedEntity] = useState<Entity | null>(null);
  const [entityTypes, setEntityTypes] = useState<string[]>([]);
  const [assetType, setAssetType] = useState('');
  const [assetName, setAssetName] = useState('');
  const [depth, setDepth] = useState(1);
  const [tabValue, setTabValue] = useState(0);
  const [graphWidth, setGraphWidth] = useState(window.innerWidth - 350); // Adjust for sidebar
  const [addQueueItemDialogOpen, setAddQueueItemDialogOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [requeueDialogOpen, setRequeueDialogOpen] = useState(false);
  const [requeueDepth, setRequeueDepth] = useState(3);
  const [graphHeight, setGraphHeight] = useState(window.innerHeight - 180); // Adjust for header

  // Group by entity type state
  const [groupByEntityType, setGroupByEntityType] = useState(false);
  const [expandedTypes, setExpandedTypes] = useState<string[]>([]);
  const [typeGroupedData, setTypeGroupedData] = useState<GraphData>({ nodes: [], links: [] });

  // Define color mapping for different entity types
  const nodeColorMap: Record<string, string> = {
    'domain': '#4285F4',
    'ip': '#EA4335',
    'dns': '#FBBC05',
    'asn': '#34A853',
    'org': '#8E44AD',
    'email': '#F39C12',
    'whois': '#1ABC9C',
    'contact': '#E74C3C',
    'dnsrecord': '#3498DB',
    'iprange': '#D35400',
    'tech': '#2ECC71',
    'social': '#9B59B6',
    'app': '#E67E22',
    'phone': '#16A085',
    'subdomain': '#2980B9',
    'nameserver': '#C0392B',
  };

  useEffect(() => {
    fetchEntityTypes();

    // Handle resize
    const handleResize = () => {
      // Full width minus padding
      setGraphWidth(window.innerWidth - 48); // 24px padding on each side

      // Calculate height dynamically based on the container size
      // We need to subtract the height of the header (~64px) and the controls section (~180px)
      setGraphHeight(window.innerHeight - 300);

      // For mobile devices, adjust the height further
      if (window.innerWidth < 600) {
        setGraphHeight(window.innerHeight - 350);
      }
    };

    // Call handleResize initially to set correct dimensions
    handleResize();

    window.addEventListener('resize', handleResize);

    // Initial graph load based on URL params
    if (id) {
      fetchEntityGraph(id);
    } else if (type) {
      fetchTypeGraph(type);
    } else {
      fetchRootGraph();
    }

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [id, type]);

  const fetchEntityTypes = async () => {
    try {
      const response = await apiService.getEntities();
      if (response.data && response.data.data) {
        const types = Array.from(new Set(response.data.data.map((entity: Entity) => entity.asset_type)));
        setEntityTypes(types as string[]);
      }
    } catch (error) {
      console.error('Error fetching entity types:', error);
    }
  };

  const fetchGraph = async () => {
    try {
      setLoading(true);
      const params = {
        asset_type: assetType,
        asset_name: assetName,
        depth: depth
      };
      console.log('Fetching graph with params:', params);
      const response = await apiService.getGraph(params);
      console.log('Graph data received:', response.data);
      setGraphData(response.data);
      setSelectedNode(null);
    } catch (error) {
      console.error('Error fetching graph data:', error);
      enqueueSnackbar('Failed to fetch graph data', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const fetchEntityGraph = async (entityId: string) => {
    try {
      setLoading(true);
      const response = await apiService.getEntityGraph(entityId);
      setGraphData(response.data);
      setSelectedNode(null);
    } catch (error) {
      console.error('Error fetching entity graph:', error);
      enqueueSnackbar('Failed to fetch entity graph', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const fetchTypeGraph = async (assetType: string) => {
    try {
      setLoading(true);
      const response = await apiService.getTypeGraph(assetType);
      setGraphData(response.data);
      setSelectedNode(null);
    } catch (error) {
      console.error('Error fetching type graph:', error);
      enqueueSnackbar('Failed to fetch type graph', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const fetchRootGraph = async () => {
    try {
      setLoading(true);
      const response = await apiService.getRootGraph();
      setGraphData(response.data);
      setSelectedNode(null);
    } catch (error) {
      console.error('Error fetching root graph:', error);
      enqueueSnackbar('Failed to fetch root graph', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Handle entity requeue
  const handleRequeueEntity = () => {
    if (!selectedNode || selectedNode.id.toString().startsWith('type:')) return;
    setRequeueDialogOpen(true);
  };

  // Handle requeue with depth
  const handleRequeueWithDepth = async () => {
    if (!selectedNode) return;

    try {
      setLoading(true);
      const response = await apiService.requeueEntity(selectedNode.id.toString(), requeueDepth);
      enqueueSnackbar(`Entity requeued successfully with depth ${response.data.depth}`, { variant: 'success' });
      setRequeueDialogOpen(false);
    } catch (error) {
      console.error('Error requeuing entity:', error);
      enqueueSnackbar('Failed to requeue entity', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Handle entity deletion
  const handleDeleteEntity = async () => {
    if (!selectedNode) return;

    try {
      setLoading(true);
      setDeleteConfirmOpen(false);

      // Only proceed with deletion if the node is not a type node
      if (!selectedNode.id.toString().startsWith('type:')) {
        const response = await apiService.deleteEntityWithChildren(selectedNode.id);
        // Show a more detailed success message
        enqueueSnackbar(
          `${response.data.message}\n` +
          `Deleted: ${response.data.deleted_count} entities\n` +
          `Preserved: ${response.data.preserved_count} entities with external relationships`,
          { variant: 'success' }
        );

        // Refresh the graph based on current view
        if (id) {
          // If we're viewing a specific entity and it was deleted, go back to the root view
          if (id === selectedNode.id) {
            navigate('/graph');
            fetchRootGraph();
          } else {
            fetchEntityGraph(id);
          }
        } else if (type) {
          fetchTypeGraph(type);
        } else {
          fetchRootGraph();
        }

        // Clear the selected node
        setSelectedNode(null);
      }
    } catch (error) {
      console.error('Error deleting entity:', error);
      enqueueSnackbar('Failed to delete entity', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Open delete confirmation dialog
  const openDeleteConfirmation = () => {
    if (selectedNode && !selectedNode.id.toString().startsWith('type:')) {
      setDeleteConfirmOpen(true);
    }
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);

    // Reset selected node
    setSelectedNode(null);

    // Load appropriate graph based on tab
    if (newValue === 0) {
      fetchGraph();
      setGroupByEntityType(false);
    } else if (newValue === 1) {
      fetchRootGraph();
      setGroupByEntityType(false);
    } else if (newValue === 2) {
      // Entity Types view
      fetchRootGraph();
      setGroupByEntityType(true);
      setExpandedTypes([]);
    }
  };

  // Effect to transform data when groupByEntityType changes
  useEffect(() => {
    if (graphData.nodes.length > 0) {
      transformGraphDataByEntityType(graphData);
    }
  }, [graphData, groupByEntityType, expandedTypes]);

  // Fetch entity details by ID
  const fetchEntityDetails = async (entityId: string) => {
    try {
      console.log('Fetching entity details for ID:', entityId);
      // Only fetch entity details for regular entity nodes, not type nodes
      if (!entityId.toString().startsWith('type:')) {
        try {
          const response = await apiService.getEntities({ id: entityId });
          console.log('Entity details response:', response);
          if (response.data && response.data.data && response.data.data.length > 0) {
            console.log('Setting selected entity:', response.data.data[0]);
            setSelectedEntity(response.data.data[0]);
            return;
          }
        } catch (apiError) {
          console.error('API error fetching entity details:', apiError);
          // Fall through to mock entity creation
        }

        // If we get here, either the API call failed or no entity was found
        // Create a mock entity for testing purposes
        console.log('Creating mock entity for ID:', entityId);
        const mockEntity: Entity = {
          id: entityId.toString(),
          asset_type: selectedNode ? selectedNode.type : 'unknown',
          asset_name: selectedNode ? selectedNode.name : 'Unknown Entity',
          do_follow: true, // Default value
          attributes: selectedNode ? selectedNode.attributes : {},
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        setSelectedEntity(mockEntity);
      } else {
        console.log('Not fetching details for type node');
        setSelectedEntity(null);
      }
    } catch (error) {
      console.error('Error in fetchEntityDetails:', error);
      setSelectedEntity(null);
    }
  };

  // Add useEffect to fetch entity details when selectedNode changes
  useEffect(() => {
    if (selectedNode && !selectedNode.id.toString().startsWith('type:')) {
      console.log('useEffect triggered to fetch entity details for:', selectedNode.id);
      fetchEntityDetails(selectedNode.id.toString());
    } else {
      // Clear selected entity when no node is selected or a type node is selected
      setSelectedEntity(null);
    }
  }, [selectedNode]);

  const handleNodeClick = (node: GraphNode) => {
    // Check if this is a type node in the grouped view
    if (groupByEntityType && node.id.startsWith('type:')) {
      const entityType = node.id.replace('type:', '');

      // Toggle expanded state for this type
      if (expandedTypes.includes(entityType)) {
        setExpandedTypes(expandedTypes.filter(type => type !== entityType));
      } else {
        setExpandedTypes([...expandedTypes, entityType]);
      }

      // Update the graph data
      transformGraphDataByEntityType(graphData);
      return;
    }

    setSelectedNode(node);
    // Note: We don't need to fetch entity details here anymore, the useEffect will handle it

    // Center the graph on the selected node
    if (graphRef.current) {
      // The ForceGraph adds x,y properties to nodes at runtime
      // We need to use any type to access these properties
      const nodeWithPos = node as any;
      if (nodeWithPos.x !== undefined && nodeWithPos.y !== undefined) {
        graphRef.current.centerAt(nodeWithPos.x, nodeWithPos.y, 1000);
        graphRef.current.zoom(2, 1000);
      }
    }
  };

  // Transform graph data to group by entity type
  const transformGraphDataByEntityType = (originalData: GraphData) => {
    if (!groupByEntityType) {
      setTypeGroupedData({ nodes: [], links: [] });
      return;
    }

    const result: GraphData = { nodes: [], links: [] };
    const entityTypeMap: Record<string, GraphNode[]> = {};

    // Group nodes by entity type
    originalData.nodes.forEach(node => {
      if (!entityTypeMap[node.type]) {
        entityTypeMap[node.type] = [];
      }
      entityTypeMap[node.type].push(node);
    });

    // Create type nodes
    Object.keys(entityTypeMap).forEach(type => {
      // Add type node
      result.nodes.push({
        id: `type:${type}`,
        name: `${type} (${entityTypeMap[type].length})`,
        type: 'entityType',
        attributes: { count: entityTypeMap[type].length }
      });

      // If this type is expanded, add its entities
      if (expandedTypes.includes(type)) {
        // Add entity nodes
        entityTypeMap[type].forEach(node => {
          result.nodes.push(node);

          // Add link from type node to entity
          result.links.push({
            source: `type:${type}`,
            target: node.id,
            label: 'contains',
            linkType: 'hierarchy'
          });
        });

        // Add links between entities of this type
        originalData.links.forEach(link => {
          const sourceNode = originalData.nodes.find(n => n.id === link.source);
          const targetNode = originalData.nodes.find(n => n.id === link.target);

          if (sourceNode && targetNode &&
              sourceNode.type === type && targetNode.type === type) {
            result.links.push(link);
          }
        });
      }
    });

    // Add links between type nodes based on relationships between their entities
    Object.keys(entityTypeMap).forEach(sourceType => {
      Object.keys(entityTypeMap).forEach(targetType => {
        if (sourceType !== targetType) {
          // Check if there are links between entities of these types
          const hasLinks = originalData.links.some(link => {
            const sourceNode = originalData.nodes.find(n => n.id === link.source);
            const targetNode = originalData.nodes.find(n => n.id === link.target);
            return sourceNode && targetNode &&
                   sourceNode.type === sourceType && targetNode.type === targetType;
          });

          if (hasLinks) {
            result.links.push({
              source: `type:${sourceType}`,
              target: `type:${targetType}`,
              label: 'related to',
              linkType: 'cross-reference'
            });
          }
        }
      });
    });

    setTypeGroupedData(result);
  };

  const getNodeColor = (node: GraphNode) => {
    // If this is a selected node, use the secondary color
    if (selectedNode && node.id === selectedNode.id) {
      return theme.palette.secondary.main;
    }

    // If this is a type node, use the color for that type
    if (node.id.toString().startsWith('type:')) {
      const entityType = node.id.toString().replace('type:', '');
      return nodeColorMap[entityType] || theme.palette.primary.main;
    }

    // Otherwise, use the color for the node's type
    return nodeColorMap[node.type] || theme.palette.grey[500];
  };

  const getLinkColor = (link: any) => {
    if (selectedNode && (link.source.id === selectedNode.id || link.target.id === selectedNode.id)) {
      return theme.palette.secondary.main;
    }

    if (link.color) return link.color;

    // Color mapping for different link types
    const colorMap: Record<string, string> = {
      'hierarchy': theme.palette.grey[600],
      'cross-reference': theme.palette.info.light,
    };

    return colorMap[link.linkType] || theme.palette.grey[400];
  };

  const renderNodeAttributes = (attributes: any) => {
    if (!attributes) return 'No attributes';

    // If the node is a queue item, display data as a string without parsing
    if (selectedNode && selectedNode.type === 'queueitem') {
      return (
        <Box sx={{ maxHeight: '300px', overflow: 'auto' }}>
          <pre style={{ margin: 0 }}>{typeof attributes === 'string' ? attributes : String(attributes)}</pre>
        </Box>
      );
    }

    // For other node types, try to parse as JSON if it's a string
    try {
      const parsed = typeof attributes === 'string' ? JSON.parse(attributes) : attributes;
      return (
        <Box sx={{ maxHeight: '300px', overflow: 'auto' }}>
          <pre style={{ margin: 0 }}>{JSON.stringify(parsed, null, 2)}</pre>
        </Box>
      );
    } catch (e) {
      // If parsing fails, display as string
      return (
        <Box sx={{ maxHeight: '300px', overflow: 'auto' }}>
          <pre style={{ margin: 0 }}>{String(attributes)}</pre>
        </Box>
      );
    }
  };

  return (
    <Layout title="Graph Visualization">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Entity Relationship Graph
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Visualize entity relationships in the system.
        </Typography>
      </Box>

      <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', height: `calc(100vh - 120px)` }}>
        {/* Controls and Stats - Top Section */}
        <Paper sx={{ p: 2, mb: 2, width: '100%' }}>
          <Grid container spacing={2}>
            {/* Controls */}
            <Grid item xs={12} md={8}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Tabs
                  value={tabValue}
                  onChange={handleTabChange}
                  indicatorColor="primary"
                  textColor="primary"
                  sx={{ mr: 2 }}
                >
                  <Tab label="Custom" />
                  <Tab label="Root Entities" />
                  <Tab label="Entity Types" />
                </Tabs>

                <Box sx={{ ml: 'auto', display: 'flex', gap: 2 }}>
                  {/* Save as Image button - always visible */}
                  <SaveGraphAsImage
                    graphRef={graphRef}
                    filename={`entity-graph-${new Date().toISOString().split('T')[0]}.svg`}
                    graphData={{
                      nodes: (groupByEntityType ? typeGroupedData : graphData).nodes.map((node: any) => ({
                        ...node,
                        // Ensure node has a string ID
                        id: String(node.id)
                      })),
                      links: (groupByEntityType ? typeGroupedData : graphData).links.map((link: any) => ({
                        ...link,
                        // Ensure source and target are strings
                        source: typeof link.source === 'object' ? String(link.source.id) : String(link.source),
                        target: typeof link.target === 'object' ? String(link.target.id) : String(link.target)
                      }))
                    }}
                  />

                  {/* Search or Refresh button based on tab */}
                  {tabValue === 0 ? (
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<SearchIcon />}
                      onClick={() => {
                        console.log('Search button clicked with params:', { assetType, assetName, depth });
                        fetchGraph();
                      }}
                      disabled={loading}
                    >
                      {loading ? <CircularProgress size={24} /> : 'Search'}
                    </Button>
                  ) : (
                    <Button
                      variant="outlined"
                      startIcon={<RefreshIcon />}
                      onClick={fetchRootGraph}
                      disabled={loading}
                    >
                      Refresh
                    </Button>
                  )}
                </Box>
              </Box>

              <TabPanel value={tabValue} index={0}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <FormControl sx={{ width: '300px' }}>
                      <InputLabel>Entity Type</InputLabel>
                      <Select
                        value={assetType}
                        onChange={(e) => {
                          console.log('Entity Type changed:', e.target.value);
                          setAssetType(e.target.value);
                        }}
                        label="Entity Type"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: 300,
                              width: '300px'
                            },
                          },
                        }}
                      >
                        <MenuItem value="">All Types</MenuItem>
                        <MenuItem value="domain">Domain</MenuItem>
                        <MenuItem value="whois">Whois</MenuItem>
                        <MenuItem value="contact">Contact</MenuItem>
                        <MenuItem value="ip">IP</MenuItem>
                        <MenuItem value="dns">DNS</MenuItem>
                        <MenuItem value="asn">ASN</MenuItem>
                        <MenuItem value="org">Organization</MenuItem>
                        <MenuItem value="dnsrecord">DNS Record</MenuItem>
                        <MenuItem value="iprange">IP Range</MenuItem>
                        <MenuItem value="email">Email</MenuItem>
                        <MenuItem value="tech">Technology</MenuItem>
                        <MenuItem value="social">Social</MenuItem>
                        <MenuItem value="app">Application</MenuItem>
                        <MenuItem value="phone">Phone</MenuItem>
                        <MenuItem value="subdomain">Subdomain</MenuItem>
                        <MenuItem value="nameserver">Name Server</MenuItem>
                        {entityTypes.filter(type => [
                          'domain', 'whois', 'contact', 'ip', 'dns', 'asn', 'org',
                          'dnsrecord', 'iprange', 'email', 'tech', 'social', 'app',
                          'phone', 'subdomain', 'nameserver'
                        ].indexOf(type) === -1).map((type) => (
                          <MenuItem key={type} value={type}>{type}</MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <TextField
                      sx={{ width: '250px' }}
                      label="Entity Name"
                      value={assetName}
                      onChange={(e) => {
                        console.log('Entity Name changed:', e.target.value);
                        setAssetName(e.target.value);
                      }}
                      placeholder="Search by name"
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl sx={{ width: '200px' }}>
                      <InputLabel>Depth</InputLabel>
                      <Select
                        value={depth}
                        onChange={(e) => {
                          console.log('Depth changed:', e.target.value);
                          setDepth(Number(e.target.value));
                        }}
                        label="Depth"
                      >
                        <MenuItem value={1}>1 (Direct connections)</MenuItem>
                        <MenuItem value={2}>2 (Connections of connections)</MenuItem>
                        <MenuItem value={3}>3 (Extended network)</MenuItem>
                        <MenuItem value={0}>All (May be slow)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </TabPanel>

              <TabPanel value={tabValue} index={1}>
                <Typography variant="body2">
                  Showing root entities (entities with no parent) and their immediate relationships.
                </Typography>
              </TabPanel>

              <TabPanel value={tabValue} index={2}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Typography variant="body2">
                    Showing entities grouped by type. Click on a type node to expand/collapse its entities.
                  </Typography>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {Object.keys(nodeColorMap).map(type => (
                      <Chip
                        key={type}
                        label={type}
                        sx={{
                          backgroundColor: nodeColorMap[type],
                          color: '#fff',
                          '&:hover': { opacity: 0.9 }
                        }}
                        onClick={() => {
                          if (expandedTypes.includes(type)) {
                            setExpandedTypes(expandedTypes.filter(t => t !== type));
                          } else {
                            setExpandedTypes([...expandedTypes, type]);
                          }
                        }}
                        variant={expandedTypes.includes(type) ? 'filled' : 'outlined'}
                      />
                    ))}
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setExpandedTypes([])}
                    >
                      Collapse All
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setExpandedTypes(Object.keys(nodeColorMap))}
                    >
                      Expand All
                    </Button>
                  </Box>
                </Box>
              </TabPanel>
            </Grid>

            {/* Stats */}
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                <Typography variant="subtitle1" gutterBottom>
                  Graph Statistics
                </Typography>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">
                    Nodes: {graphData.nodes.length}
                  </Typography>
                  <Typography variant="body2">
                    Links: {graphData.links.length}
                  </Typography>
                </Box>

                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  Node Types:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {Array.from(new Set(graphData.nodes.map(node => node.type))).map(type => (
                    <Chip
                      key={type}
                      label={`${type} (${graphData.nodes.filter(n => n.type === type).length})`}
                      size="small"
                      onClick={() => navigate(`/graph/type/${type}`)}
                    />
                  ))}
                </Box>

                {selectedNode && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Selected: {selectedNode.name}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Button
                        size="small"
                        variant="outlined"
                        color="primary"
                        onClick={() => navigate(`/graph/entity/${selectedNode.id}`)}
                      >
                        Focus on this Entity
                      </Button>
                      <Tooltip title="Add a new queue item with this entity as parent">
                        <Button
                          size="small"
                          variant="outlined"
                          color="secondary"
                          startIcon={<AddIcon />}
                          onClick={() => setAddQueueItemDialogOpen(true)}
                        >
                          Add to Queue
                        </Button>
                      </Tooltip>
                      {/* Only show delete button for regular entity nodes, not type nodes */}
                      {!selectedNode.id.toString().startsWith('type:') && (
                        <Button
                          size="small"
                          variant="outlined"
                          color="error"
                          startIcon={<DeleteIcon />}
                          onClick={openDeleteConfirmation}
                        >
                          Delete Entity
                        </Button>
                      )}
                    </Box>
                  </Box>
                )}
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Graph visualization - Takes remaining space */}
        <Paper
          sx={{
            flexGrow: 1,
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
            boxSizing: 'border-box',
            minHeight: `${graphHeight}px`
          }}
        >
          {loading ? (
            <CircularProgress />
          ) : graphData.nodes.length === 0 ? (
            <Typography variant="body1" color="text.secondary">
              No data to display. Try adjusting your search parameters.
            </Typography>
          ) : (
            <div id="entity-relationship-graph">
              <ForceGraph2D
                ref={graphRef}
                graphData={groupByEntityType ? typeGroupedData : graphData}
                nodeLabel="name"
                nodeColor={getNodeColor}
                linkColor={getLinkColor}
                linkDirectionalArrowLength={3.5}
                linkDirectionalArrowRelPos={1}
                nodeId="id"
                nodeAutoColorBy="type"
                nodeCanvasObjectMode={() => 'after'}
                nodeCanvasObject={(node: any, ctx: any, globalScale: number) => {
                  const label = node.name as string;
                  const fontSize = 12 / globalScale;
                  ctx.font = `${fontSize}px Sans-Serif`;
                  const textWidth = ctx.measureText(label).width;
                  const bckgDimensions = [textWidth, fontSize].map(n => n + fontSize * 0.2); // some padding

                  if (node.x !== undefined && node.y !== undefined) {
                    // Check if this is an entity type node
                    const isTypeNode = node.id.toString().startsWith('type:');
                    const nodeSize = isTypeNode ? 10 : 5;

                    // Draw node
                    ctx.beginPath();
                    if (isTypeNode) {
                      // Draw a hexagon for type nodes
                      const sides = 6;
                      const a = 2 * Math.PI / sides;
                      ctx.moveTo(node.x + nodeSize * Math.cos(0), node.y + nodeSize * Math.sin(0));
                      for (let i = 1; i <= sides; i++) {
                        ctx.lineTo(node.x + nodeSize * Math.cos(a * i), node.y + nodeSize * Math.sin(a * i));
                      }
                    } else {
                      // Draw a circle for regular nodes
                      ctx.arc(node.x, node.y, nodeSize, 0, 2 * Math.PI);
                    }

                    // Set node color
                    if (isTypeNode) {
                      // Use a gradient for type nodes
                      const entityType = node.id.toString().replace('type:', '');
                      const color = nodeColorMap[entityType] || theme.palette.primary.main;
                      const gradient = ctx.createRadialGradient(
                        node.x, node.y, 0,
                        node.x, node.y, nodeSize
                      );
                      gradient.addColorStop(0, color);
                      gradient.addColorStop(1, theme.palette.background.paper);
                      ctx.fillStyle = gradient;
                    } else {
                      ctx.fillStyle = nodeColorMap[node.type] || theme.palette.primary.main;
                    }
                    ctx.fill();

                    // Draw outline for type nodes
                    if (isTypeNode) {
                      ctx.strokeStyle = theme.palette.grey[800];
                      ctx.lineWidth = 1.5 / globalScale;
                      ctx.stroke();

                      // Add expand/collapse indicator
                      const entityType = node.id.toString().replace('type:', '');
                      const isExpanded = expandedTypes.includes(entityType);
                      ctx.beginPath();
                      ctx.arc(node.x, node.y, nodeSize / 2, 0, 2 * Math.PI);
                      ctx.fillStyle = isExpanded ? theme.palette.error.main : theme.palette.success.main;
                      ctx.fill();
                    }

                    // Draw label background
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                    ctx.fillRect(
                      node.x - bckgDimensions[0] / 2,
                      node.y - bckgDimensions[1] / 2 - 10,
                      bckgDimensions[0],
                      bckgDimensions[1]
                    );

                    // Draw label text
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillStyle = 'rgba(0, 0, 0, 1)';
                    ctx.fillText(label, node.x, node.y - 10);
                  }
                  (node as any).__bckgDimensions = bckgDimensions; // to re-use in pointer area
                }}
              nodePointerAreaPaint={(node: any, color: string, ctx: any) => {
                  ctx.fillStyle = color;
                  const bckgDimensions = (node as any).__bckgDimensions;
                  if (bckgDimensions && node.x !== undefined && node.y !== undefined) {
                    // Check if this is an entity type node
                    const isTypeNode = node.id.toString().startsWith('type:');
                    const nodeSize = isTypeNode ? 10 : 5;

                    // Draw pointer area for the label
                    ctx.fillRect(
                      node.x - bckgDimensions[0] / 2,
                      node.y - bckgDimensions[1] / 2 - 10,
                      bckgDimensions[0],
                      bckgDimensions[1]
                    );

                    // Draw pointer area for the node
                    ctx.beginPath();
                    if (isTypeNode) {
                      // Draw a hexagon for type nodes
                      const sides = 6;
                      const a = 2 * Math.PI / sides;
                      ctx.moveTo(node.x + nodeSize * Math.cos(0), node.y + nodeSize * Math.sin(0));
                      for (let i = 1; i <= sides; i++) {
                        ctx.lineTo(node.x + nodeSize * Math.cos(a * i), node.y + nodeSize * Math.sin(a * i));
                      }
                    } else {
                      // Draw a circle for regular nodes
                      ctx.arc(node.x, node.y, nodeSize, 0, 2 * Math.PI);
                    }
                    ctx.fill();
                  }
                }}
              linkLabel="label"
              onNodeClick={handleNodeClick}
              width={graphWidth}
              height={graphHeight}
              cooldownTicks={100}
              nodeRelSize={3}
              linkWidth={1}
              backgroundColor={theme.palette.background.paper}
            />
            </div>
          )}
        </Paper>

        {/* Node details dialog */}
        <Dialog
          open={!!selectedNode}
          onClose={() => setSelectedNode(null)}
          maxWidth="sm"
          fullWidth
        >
          {selectedNode && (
            <>
              <DialogTitle>
                Node Details: {selectedNode.name}
              </DialogTitle>
              <DialogContent dividers>
                <Typography variant="subtitle2">ID</Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  {selectedNode.id}
                </Typography>

                <Typography variant="subtitle2">Type</Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  <Chip label={selectedNode.type} size="small" />
                </Typography>

                {/* Only show DoFollow toggle for regular entity nodes, not type nodes */}
                {/* Rendering node details dialog with selectedEntity */}
                {!selectedNode.id.toString().startsWith('type:') && selectedEntity ? (
                  <>
                    <Typography variant="subtitle2">Crawl Settings</Typography>
                    <Box sx={{ my: 2 }}>
                      <EntityDoFollowToggle
                        entity={selectedEntity}
                        onUpdate={(updatedEntity) => {
                          // Update the selected entity
                          setSelectedEntity(updatedEntity);

                          // Show success message
                          enqueueSnackbar(`Entity ${updatedEntity.do_follow ? 'will' : 'will not'} follow children for crawling`, {
                            variant: 'success',
                            autoHideDuration: 3000
                          });
                        }}
                      />
                    </Box>
                    <Divider sx={{ my: 2 }} />
                  </>
                ) : (
                  !selectedNode.id.toString().startsWith('type:') && (
                    <Typography variant="body2" color="text.secondary">
                      Loading entity details...
                    </Typography>
                  )
                )}

                <Typography variant="subtitle2">Attributes</Typography>
                {renderNodeAttributes(selectedNode.attributes)}
              </DialogContent>
              <DialogActions>
                {/* Save as Image button - always visible */}
                <SaveGraphAsImage
                  graphRef={graphRef}
                  filename={`entity-graph-${selectedNode.id}-${new Date().toISOString().split('T')[0]}.svg`}
                  graphData={groupByEntityType ? typeGroupedData : graphData}
                />

                {/* Only show delete button for regular entity nodes, not type nodes */}
                {!selectedNode.id.toString().startsWith('type:') && (
                  <>
                    <Button
                      color="primary"
                      startIcon={<ReplayIcon />}
                      onClick={handleRequeueEntity}
                      sx={{ mr: 1 }}
                    >
                      Requeue Entity
                    </Button>
                    <Button
                      color="error"
                      startIcon={<DeleteIcon />}
                      onClick={openDeleteConfirmation}
                    >
                      Delete Entity
                    </Button>
                  </>
                )}
                <Button
                  onClick={() => navigate(`/graph/entity/${selectedNode.id}`)}
                  color="primary"
                >
                  Focus on this Entity
                </Button>
                <Button onClick={() => setSelectedNode(null)}>
                  Close
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>

        {/* Delete confirmation dialog */}
        <Dialog
          open={deleteConfirmOpen}
          onClose={() => setDeleteConfirmOpen(false)}
          maxWidth="sm"
        >
          <DialogTitle>Confirm Deletion</DialogTitle>
          <DialogContent>
            <Typography variant="body1">
              Are you sure you want to delete this entity and its children? This action cannot be undone.
            </Typography>
            <Typography variant="body2" sx={{ mt: 2 }}>
              <strong>What will happen:</strong>
              <ul>
                <li>This entity will be deleted</li>
                <li>Children with no external relationships will be deleted recursively</li>
                <li>Children with external relationships will be preserved but disconnected from this entity</li>
                <li>This preservation rule applies at all levels of the hierarchy</li>
                <li>All relationships involving this entity will be deleted</li>
              </ul>
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
            <Button color="error" onClick={handleDeleteEntity}>Delete</Button>
          </DialogActions>
        </Dialog>
        {/* Add Queue Item Dialog */}
        <AddQueueItemDialog
          open={addQueueItemDialogOpen}
          onClose={() => setAddQueueItemDialogOpen(false)}
          onSuccess={() => {
            enqueueSnackbar('Queue item added successfully', { variant: 'success' });
            setAddQueueItemDialogOpen(false);
          }}
          initialParentId={selectedNode?.id || ''}
        />

        {/* Requeue Dialog */}
        <Dialog open={requeueDialogOpen} onClose={() => setRequeueDialogOpen(false)} maxWidth="sm">
          <DialogTitle>Requeue Entity with Depth</DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              <Typography variant="body1" gutterBottom>
                Set the maximum crawl depth for this entity. Higher values will crawl deeper into the entity's relationships.
              </Typography>
              <TextField
                fullWidth
                label="Max Depth"
                type="number"
                value={requeueDepth}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  if (!isNaN(value) && value >= 0) {
                    setRequeueDepth(value);
                  }
                }}
                // Set min directly as a prop
                // This is a workaround for the deprecated inputProps
                // @ts-ignore - min is a valid prop for number inputs
                min={0}
                helperText="Higher values will crawl deeper into the entity's relationships. Default is 3."
                sx={{ mt: 2 }}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setRequeueDialogOpen(false)}>Cancel</Button>
            <Button
              onClick={handleRequeueWithDepth}
              color="primary"
              variant="contained"
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Requeue'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Layout>
  );
};

export default GraphPage;
