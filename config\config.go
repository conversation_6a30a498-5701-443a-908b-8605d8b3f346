package config

import (
	"os"
	"strconv"
	"strings"

	"github.com/crawler/db"
)

// Config holds the application configuration
type Config struct {
	DB               db.Config
	Port             string
	WorkerPool       int
	AutoStartCrawler bool
	Mode             string   // "master" or "slave"
	MasterURL        string   // URL of the master node (for slave mode)
	APIKey           string   // API key for authentication
	ManualCrawlTypes []string // Entity types that should only be crawled manually
}

// LoadConfig loads configuration from environment variables
func LoadConfig() Config {
	// Default values
	config := Config{
		DB: db.Config{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5432"),
			User:     getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", "postgres"),
			DBName:   getEnv("DB_NAME", "crawler"),
			SSLMode:  getEnv("DB_SSLMODE", "disable"),
		},
		Port:             getEnv("PORT", "8080"),
		WorkerPool:       getEnvAsInt("WORKER_POOL", 5),
		AutoStartCrawler: getEnvAsBool("AUTO_START_CRAWLER", true),
		Mode:             getEnv("CRAWLER_MODE", "master"),
		MasterURL:        getEnv("MASTER_URL", ""),
		APIKey:           getEnv("API_KEY", ""),
		ManualCrawlTypes: getEnvAsStringSlice("MANUAL_CRAWL_TYPES", []string{}),
	}

	return config
}

// Helper function to get an environment variable or a default value
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// Helper function to get an environment variable as an integer
func getEnvAsInt(key string, defaultValue int) int {
	valueStr := getEnv(key, "")
	if value, err := strconv.Atoi(valueStr); err == nil {
		return value
	}
	return defaultValue
}

// Helper function to get an environment variable as a boolean
func getEnvAsBool(key string, defaultValue bool) bool {
	valueStr := getEnv(key, "")
	if valueStr == "" {
		return defaultValue
	}
	value, err := strconv.ParseBool(valueStr)
	if err != nil {
		return defaultValue
	}
	return value
}

// Helper function to get an environment variable as a string slice
func getEnvAsStringSlice(key string, defaultValue []string) []string {
	valueStr := getEnv(key, "")
	if valueStr == "" {
		return defaultValue
	}
	// Split by comma and trim spaces
	result := []string{}
	for _, item := range strings.Split(valueStr, ",") {
		trimmed := strings.TrimSpace(item)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}
	return result
}
