-- Revert to the previous version of the function
CREATE OR REPLACE FUNCTION delete_entity_with_children(
    p_entity_id TEXT
)
RETURNS TABLE(deleted_count INT) AS $$
DECLARE
    v_count INT := 0;
    v_child RECORD;
    v_has_external_relations BOOLEAN;
BEGIN
    -- Check if entity exists
    IF NOT EXISTS (SELECT 1 FROM entities WHERE id = p_entity_id) THEN
        RETURN QUERY SELECT v_count;
        RETURN;
    END IF;

    -- Process children first (depth-first approach)
    FOR v_child IN (
        SELECT id FROM entities WHERE parent_id = p_entity_id
    ) LOOP
        -- Check if child has relationships with entities other than its parent
        SELECT EXISTS (
            SELECT 1 
            FROM entity_relationships 
            WHERE (from_entity_id = v_child.id OR to_entity_id = v_child.id)
            AND (from_entity_id != p_entity_id AND to_entity_id != p_entity_id)
        ) INTO v_has_external_relations;

        -- If child has no external relationships, delete it recursively
        IF NOT v_has_external_relations THEN
            v_count := v_count + (SELECT * FROM delete_entity_with_children(v_child.id));
        ELSE
            -- Child has external relationships, so just update its parent reference
            UPDATE entities SET parent_id = NULL WHERE id = v_child.id;
            RAISE NOTICE 'Preserved entity % because it has external relationships', v_child.id;
        END IF;
    END LOOP;

    -- Delete all relationships where this entity is involved
    DELETE FROM entity_relationships
    WHERE from_entity_id = p_entity_id OR to_entity_id = p_entity_id;

    -- Delete the entity
    DELETE FROM entities WHERE id = p_entity_id;
    
    -- Increment count for this entity
    v_count := v_count + 1;

    -- Return the total count of deleted entities
    RETURN QUERY SELECT v_count;
END;
$$ LANGUAGE plpgsql;
