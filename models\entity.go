package models

import (
	"encoding/json"
	"time"

	"gorm.io/datatypes"
)

// Entity represents a node in the system
type Entity struct {
	ID         string         `gorm:"primaryKey;type:text;column:id"`
	AssetType  string         `gorm:"column:asset_type;size:100;not null;index"` // Indexing type is important
	AssetName  string         `gorm:"column:asset_name;type:text"`
	ParentID   *string        `gorm:"column:parent_id;type:text;index"`
	Attributes datatypes.JSON `gorm:"type:jsonb;column:attributes"`  // Stores OrgAttributes, ServiceAttributes, etc. as JSON
	DoFollow   bool           `gorm:"column:do_follow;default:true"` // Controls whether crawler should follow this entity to crawl its children
	CreatedAt  time.Time      `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt  time.Time      `gorm:"column:updated_at;autoUpdateTime"`

	// --- Relationships (Hierarchy & Cross-Refs) ---
	// These remain the same, managed by ParentID and the entity_relationships table.
	Parent            *Entity               `gorm:"foreignKey:ParentID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL;"`
	Children          []*Entity             `gorm:"foreignKey:ParentID"`
	RelationshipsFrom []*EntityRelationship `gorm:"foreignKey:FromEntityID"`
	RelationshipsTo   []*EntityRelationship `gorm:"foreignKey:ToEntityID"`
}

// SetAttributes marshals the provided struct into the Attributes field.
// Returns an error if marshalling fails.
func (e *Entity) SetAttributes(data interface{}) error {
	bytes, err := json.Marshal(data)
	if err != nil {
		return err
	}
	e.Attributes = datatypes.JSON(bytes)
	return nil
}

// Decode unmarshals the Attributes field into the provided struct.
func (e *Entity) Decode(data interface{}) error {
	err := json.Unmarshal(e.Attributes, data)
	return err
}

// TableName specifies the table name for GORM.
func (Entity) TableName() string {
	return "entities"
}
