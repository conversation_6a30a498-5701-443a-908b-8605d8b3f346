import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Button,
  IconButton,
  Chip,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Divider
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import ReplayIcon from '@mui/icons-material/Replay';
import DeleteIcon from '@mui/icons-material/Delete';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout/Layout';
import EntityDoFollowToggle from '../components/EntityDoFollowToggle';
import apiService, { Entity } from '../services/api';
import { useSnackbar } from 'notistack';

const EntitiesPage: React.FC = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const [entities, setEntities] = useState<Entity[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [totalCount, setTotalCount] = useState(0);
  const [selectedEntity, setSelectedEntity] = useState<Entity | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [entityTypes, setEntityTypes] = useState<string[]>([]);
  const [requeueDialogOpen, setRequeueDialogOpen] = useState(false);
  const [requeueDepth, setRequeueDepth] = useState(3);

  useEffect(() => {
    fetchEntities();
  }, [page, rowsPerPage]); // Refetch when page or rowsPerPage changes

  const fetchEntities = async (filters?: { asset_type?: string; asset_name?: string }) => {
    try {
      setLoading(true);

      // Add pagination parameters
      const paginationParams = {
        page: page + 1, // API uses 1-based indexing
        limit: rowsPerPage
      };

      const response = await apiService.getEntities({
        ...filters,
        ...paginationParams
      });

      // Update state with paginated data
      setEntities(response.data.data);
      setTotalCount(response.data.total_count);

      // Extract unique entity types if this is the first load
      if (entityTypes.length === 0) {
        const types = Array.from(new Set(response.data.data.map((entity: Entity) => entity.asset_type)));
        setEntityTypes(types);
      }
    } catch (error) {
      console.error('Error fetching entities:', error);
      enqueueSnackbar('Failed to fetch entities', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearch = () => {
    // Reset to first page when searching
    setPage(0);

    const filters: { asset_type?: string; asset_name?: string } = {};
    if (filterType) filters.asset_type = filterType;
    if (searchTerm) filters.asset_name = searchTerm;
    fetchEntities(filters);
  };

  const handleViewDetails = (entity: Entity) => {
    setSelectedEntity(entity);
    setDetailsOpen(true);
  };

  const handleViewGraph = (entityId: string) => {
    navigate(`/graph/entity/${entityId}`);
  };

  const handleRequeueEntity = async (entityId: string) => {
    // Open the requeue dialog to set depth
    setSelectedEntity(entities.find(e => e.id === entityId) || null);
    setRequeueDialogOpen(true);
  };

  const handleRequeueWithDepth = async () => {
    if (!selectedEntity) return;

    try {
      setLoading(true);
      const response = await apiService.requeueEntity(selectedEntity.id, requeueDepth);
      enqueueSnackbar(`Entity requeued successfully with depth ${response.data.depth}`, { variant: 'success' });
      setRequeueDialogOpen(false);

      // Close the details dialog if it's open
      if (detailsOpen) {
        setDetailsOpen(false);
      }
    } catch (error) {
      console.error('Error requeuing entity:', error);
      enqueueSnackbar('Failed to requeue entity', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [entityToDelete, setEntityToDelete] = useState<string | null>(null);

  const handleDeleteEntity = async (entityId: string) => {
    // Set the entity to delete and open the confirmation dialog
    setEntityToDelete(entityId);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteEntity = async () => {
    if (!entityToDelete) return;

    try {
      setLoading(true);
      const response = await apiService.deleteEntityWithChildren(entityToDelete);

      // Create a more detailed success message
      const successMessage = (
        <div>
          <strong>{response.data.entity_type}: {response.data.entity_name}</strong> deleted successfully
          <ul>
            <li><strong>{response.data.deleted_count - 1}</strong> child entities deleted</li>
            <li><strong>{response.data.preserved_count}</strong> entities preserved (have external relationships)</li>
            <li>Total descendants: {response.data.total_descendants}</li>
            <li>Descendants with external relationships: {response.data.external_relations}</li>
          </ul>
        </div>
      );

      enqueueSnackbar(successMessage, {
        variant: 'success',
        autoHideDuration: 5000 // Give users more time to read the detailed message
      });

      // Refresh the entity list
      fetchEntities();

      // Close the details dialog if it's open
      if (detailsOpen && selectedEntity?.id === entityToDelete) {
        setDetailsOpen(false);
      }
    } catch (error) {
      console.error('Error deleting entity:', error);
      enqueueSnackbar('Failed to delete entity', { variant: 'error' });
    } finally {
      setLoading(false);
      setDeleteDialogOpen(false);
      setEntityToDelete(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const renderEntityAttributes = (attributes: any) => {
    if (!attributes) return 'No attributes';

    try {
      const parsed = typeof attributes === 'string' ? JSON.parse(attributes) : attributes;
      return (
        <Box sx={{ maxHeight: '300px', overflow: 'auto' }}>
          <pre style={{ margin: 0 }}>{JSON.stringify(parsed, null, 2)}</pre>
        </Box>
      );
    } catch (e) {
      return String(attributes);
    }
  };

  return (
    <Layout title="Entities">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Entities
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          View and manage entities in the system.
        </Typography>
      </Box>

      {/* Search and Filter */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Search by Name"
              variant="outlined"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Filter by Type</InputLabel>
              <Select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                label="Filter by Type"
              >
                <MenuItem value="">All Types</MenuItem>
                {entityTypes.map((type) => (
                  <MenuItem key={type} value={type}>{type}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              startIcon={<SearchIcon />}
              onClick={handleSearch}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Search'}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Entities Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Name</TableCell>
              <TableCell>Parent ID</TableCell>
              <TableCell>Created At</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : entities.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  No entities found
                </TableCell>
              </TableRow>
            ) : (
              entities.map((entity) => (
                  <TableRow key={entity.id}>
                    <TableCell>{entity.id.substring(0, 8)}...</TableCell>
                    <TableCell>
                      <Chip label={entity.asset_type} size="small" />
                    </TableCell>
                    <TableCell>{entity.asset_name}</TableCell>
                    <TableCell>
                      {entity.parent_id ? entity.parent_id.substring(0, 8) + '...' : 'None'}
                    </TableCell>
                    <TableCell>{formatDate(entity.created_at)}</TableCell>
                    <TableCell>
                      <IconButton
                        color="primary"
                        onClick={() => handleViewDetails(entity)}
                        title="View Details"
                      >
                        <VisibilityIcon />
                      </IconButton>
                      <IconButton
                        color="secondary"
                        onClick={() => handleViewGraph(entity.id)}
                        title="View Graph"
                      >
                        <AccountTreeIcon />
                      </IconButton>
                      <IconButton
                        color="primary"
                        onClick={() => handleRequeueEntity(entity.id)}
                        title="Requeue Entity"
                      >
                        <ReplayIcon />
                      </IconButton>
                      <IconButton
                        color="error"
                        onClick={() => handleDeleteEntity(entity.id)}
                        title="Delete Entity"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
            )}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50, 100]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Entity Details Dialog */}
      <Dialog open={detailsOpen} onClose={() => setDetailsOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Entity Details</DialogTitle>
        <DialogContent dividers>
          {selectedEntity && (
            <Box>
              <Typography variant="subtitle1">ID</Typography>
              <Typography variant="body2" paragraph>{selectedEntity.id}</Typography>

              <Typography variant="subtitle1">Type</Typography>
              <Typography variant="body2" paragraph>{selectedEntity.asset_type}</Typography>

              <Typography variant="subtitle1">Name</Typography>
              <Typography variant="body2" paragraph>{selectedEntity.asset_name}</Typography>

              <Typography variant="subtitle1">Parent ID</Typography>
              <Typography variant="body2" paragraph>
                {selectedEntity.parent_id || 'None'}
              </Typography>

              <Typography variant="subtitle1">Created At</Typography>
              <Typography variant="body2" paragraph>
                {formatDate(selectedEntity.created_at)}
              </Typography>

              <Typography variant="subtitle1">Updated At</Typography>
              <Typography variant="body2" paragraph>
                {formatDate(selectedEntity.updated_at)}
              </Typography>

              <Typography variant="subtitle1">Crawl Settings</Typography>
              <Box sx={{ my: 2 }}>
                <EntityDoFollowToggle
                  entity={selectedEntity}
                  onUpdate={(updatedEntity) => {
                    // Update the selected entity with the updated one
                    setSelectedEntity(updatedEntity);

                    // Also update the entity in the entities list
                    setEntities(entities.map(e =>
                      e.id === updatedEntity.id ? updatedEntity : e
                    ));
                  }}
                />
              </Box>
              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle1">Attributes</Typography>
              {renderEntityAttributes(selectedEntity.attributes)}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          {selectedEntity && (
            <>
              <Button
                color="error"
                startIcon={<DeleteIcon />}
                onClick={() => {
                  handleDeleteEntity(selectedEntity.id);
                }}
              >
                Delete Entity
              </Button>
              <Button
                color="primary"
                startIcon={<ReplayIcon />}
                onClick={() => {
                  handleRequeueEntity(selectedEntity.id);
                }}
              >
                Requeue Entity
              </Button>
              <Button
                color="secondary"
                startIcon={<AccountTreeIcon />}
                onClick={() => handleViewGraph(selectedEntity.id)}
              >
                View Graph
              </Button>
            </>
          )}
          <Button onClick={() => setDetailsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Requeue Dialog */}
      <Dialog open={requeueDialogOpen} onClose={() => setRequeueDialogOpen(false)} maxWidth="sm">
        <DialogTitle>Requeue Entity with Depth</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Typography variant="body1" gutterBottom>
              Set the maximum crawl depth for this entity. Higher values will crawl deeper into the entity's relationships.
            </Typography>

            <TextField
              fullWidth
              label="Max Depth"
              type="number"
              value={requeueDepth}
              onChange={(e) => {
                const value = parseInt(e.target.value);
                if (!isNaN(value) && value >= 0) {
                  setRequeueDepth(value);
                }
              }}
              inputProps={{ min: 0 }}
              helperText="Higher values will crawl deeper into the entity's relationships. Default is 3."
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRequeueDialogOpen(false)}>Cancel</Button>
          <Button
            color="primary"
            variant="contained"
            onClick={handleRequeueWithDepth}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Requeue'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this entity and its children? This action cannot be undone.
            <br /><br />
            <strong>Important:</strong> The deletion process will:
            <ul>
              <li>Delete the selected entity</li>
              <li>Recursively delete all child entities in the hierarchy</li>
              <li>Preserve only child entities that have relationships with entities whose root parent is different from the selected entity</li>
              <li>Update preserved entities to remove their parent reference</li>
            </ul>
            <br />
            This ensures that entities are only preserved if they have meaningful connections outside the deletion tree.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={confirmDeleteEntity}
            color="error"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default EntitiesPage;
