import React from 'react';
import {
  Drawer as MuiDrawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  Toolbar,
  useTheme
} from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import StorageIcon from '@mui/icons-material/Storage';
import QueueIcon from '@mui/icons-material/Queue';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import BlockIcon from '@mui/icons-material/Block';
import BarChartIcon from '@mui/icons-material/BarChart';
import SettingsIcon from '@mui/icons-material/Settings';
import { useNavigate, useLocation } from 'react-router-dom';

interface DrawerProps {
  open: boolean;
  onClose: () => void;
  drawerWidth: number;
  variant: 'permanent' | 'persistent' | 'temporary';
}

const Drawer: React.FC<DrawerProps> = ({ open, onClose, drawerWidth, variant }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    { text: 'Dashboard', icon: <DashboardIcon />, path: '/' },
    { text: 'Entities', icon: <StorageIcon />, path: '/entities' },
    { text: 'Queue', icon: <QueueIcon />, path: '/queue' },
    { text: 'Graph', icon: <AccountTreeIcon />, path: '/graph' },
    { text: 'Blacklist', icon: <BlockIcon />, path: '/blacklist' },
    { text: 'Statistics', icon: <BarChartIcon />, path: '/stats' },
    { text: 'Settings', icon: <SettingsIcon />, path: '/settings' },
  ];

  const handleNavigation = (path: string) => {
    navigate(path);
    if (variant === 'temporary') {
      onClose();
    }
  };

  const drawerContent = (
    <>
      <Toolbar />
      <Box sx={{ overflow: 'auto' }}>
        <List>
          {menuItems.map((item) => (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                selected={location.pathname === item.path}
                onClick={() => handleNavigation(item.path)}
              >
                <ListItemIcon sx={{
                  color: location.pathname === item.path ? theme.palette.primary.main : 'inherit'
                }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText primary={item.text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
        <Divider />
      </Box>
    </>
  );

  return (
    <MuiDrawer
      variant={variant}
      open={open}
      onClose={onClose}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
        },
      }}
    >
      {drawerContent}
    </MuiDrawer>
  );
};

export default Drawer;
