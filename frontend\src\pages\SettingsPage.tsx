import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import Layout from '../components/Layout/Layout';
import { useSnackbar } from 'notistack';

const SettingsPage: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    apiUrl: 'http://localhost:8080',
    workerCount: 5,
    refreshInterval: 30,
    darkMode: false,
    notifications: true
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setSettings({
      ...settings,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSelectChange = (e: React.ChangeEvent<{ name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    if (name) {
      setSettings({
        ...settings,
        [name]: value
      });
    }
  };

  const handleSave = () => {
    setLoading(true);
    
    // Simulate saving settings
    setTimeout(() => {
      setLoading(false);
      enqueueSnackbar('Settings saved successfully', { variant: 'success' });
      
      // In a real app, you would save these settings to localStorage or a backend API
      localStorage.setItem('crawlerSettings', JSON.stringify(settings));
    }, 1000);
  };

  return (
    <Layout title="Settings">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Settings
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Configure your crawler system settings.
        </Typography>
      </Box>

      <Alert severity="info" sx={{ mb: 4 }}>
        These settings are for demonstration purposes only. In a production environment, 
        these would be connected to your backend configuration.
      </Alert>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          API Configuration
        </Typography>
        <Divider sx={{ mb: 3 }} />
        
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="API URL"
              name="apiUrl"
              value={settings.apiUrl}
              onChange={handleChange}
              helperText="The base URL for the crawler API"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Worker Count</InputLabel>
              <Select
                name="workerCount"
                value={settings.workerCount}
                onChange={handleSelectChange as any}
                label="Worker Count"
              >
                <MenuItem value={1}>1 (Minimal)</MenuItem>
                <MenuItem value={3}>3 (Low)</MenuItem>
                <MenuItem value={5}>5 (Medium)</MenuItem>
                <MenuItem value={10}>10 (High)</MenuItem>
                <MenuItem value={20}>20 (Maximum)</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Typography variant="h6" gutterBottom>
          UI Settings
        </Typography>
        <Divider sx={{ mb: 3 }} />
        
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Auto-refresh Interval</InputLabel>
              <Select
                name="refreshInterval"
                value={settings.refreshInterval}
                onChange={handleSelectChange as any}
                label="Auto-refresh Interval"
              >
                <MenuItem value={0}>Disabled</MenuItem>
                <MenuItem value={10}>10 seconds</MenuItem>
                <MenuItem value={30}>30 seconds</MenuItem>
                <MenuItem value={60}>1 minute</MenuItem>
                <MenuItem value={300}>5 minutes</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.darkMode}
                    onChange={handleChange}
                    name="darkMode"
                    color="primary"
                  />
                }
                label="Dark Mode"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications}
                    onChange={handleChange}
                    name="notifications"
                    color="primary"
                  />
                }
                label="Enable Notifications"
              />
            </Box>
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={loading ? <CircularProgress size={24} color="inherit" /> : <SaveIcon />}
            onClick={handleSave}
            disabled={loading}
          >
            Save Settings
          </Button>
        </Box>
      </Paper>
    </Layout>
  );
};

export default SettingsPage;
