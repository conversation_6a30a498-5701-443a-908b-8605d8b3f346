@echo off
setlocal enabledelayedexpansion

REM Script to check Nginx logs for debugging

REM Display usage information
if "%~1"=="" (
  echo Usage: %0 [option]
  echo Options:
  echo   frontend  - Check frontend Nginx logs
  echo   api       - Check API Nginx logs
  echo   all       - Check all Nginx logs
  echo   help      - Show this help message
  exit /b 1
)

REM Process the option
if "%~1"=="frontend" (
  echo Checking frontend Nginx logs...
  docker-compose exec frontend tail -f /var/log/nginx/error.log
) else if "%~1"=="api" (
  echo Checking API Nginx logs...
  docker-compose exec nginx-api tail -f /var/log/nginx/error.log
) else if "%~1"=="all" (
  echo Checking all Nginx logs...
  echo Frontend Nginx error log:
  docker-compose exec frontend tail -n 20 /var/log/nginx/error.log
  echo.
  echo Frontend Nginx access log:
  docker-compose exec frontend tail -n 20 /var/log/nginx/access.log
  echo.
  echo API Nginx error log:
  docker-compose exec nginx-api tail -n 20 /var/log/nginx/error.log
  echo.
  echo API Nginx access log:
  docker-compose exec nginx-api tail -n 20 /var/log/nginx/access.log
) else if "%~1"=="help" (
  echo Usage: %0 [option]
  echo Options:
  echo   frontend  - Check frontend Nginx logs
  echo   api       - Check API Nginx logs
  echo   all       - Check all Nginx logs
  echo   help      - Show this help message
  exit /b 0
) else (
  echo Error: Unknown option '%~1'
  echo Usage: %0 [option]
  echo Options:
  echo   frontend  - Check frontend Nginx logs
  echo   api       - Check API Nginx logs
  echo   all       - Check all Nginx logs
  echo   help      - Show this help message
  exit /b 1
)
