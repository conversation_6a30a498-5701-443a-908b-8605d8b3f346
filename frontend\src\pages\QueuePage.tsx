import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  IconButton,
  Chip,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { Grid } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import RefreshIcon from '@mui/icons-material/Refresh';
import InfoIcon from '@mui/icons-material/Info';
import Layout from '../components/Layout/Layout';
import apiService, { QueueItem } from '../services/api';
import { useSnackbar } from 'notistack';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`queue-tabpanel-${index}`}
      aria-labelledby={`queue-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
};

const QueuePage: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [tabValue, setTabValue] = useState(0);
  const [pendingItems, setPendingItems] = useState<QueueItem[]>([]);
  const [processingItems, setProcessingItems] = useState<QueueItem[]>([]);
  const [processedItems, setProcessedItems] = useState<QueueItem[]>([]);
  const [pendingTotal, setPendingTotal] = useState(0);
  const [processingTotal, setProcessingTotal] = useState(0);
  const [processedTotal, setProcessedTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [pendingPage, setPendingPage] = useState(0);
  const [pendingRowsPerPage, setPendingRowsPerPage] = useState(10);
  const [processingPage, setProcessingPage] = useState(0);
  const [processingRowsPerPage, setProcessingRowsPerPage] = useState(10);
  const [processedPage, setProcessedPage] = useState(0);
  const [processedRowsPerPage, setProcessedRowsPerPage] = useState(10);
  const [selectedItem, setSelectedItem] = useState<QueueItem | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [newItem, setNewItem] = useState({
    data_type: 'domain',
    parent_id: '',
    data: 'example.com',
    max_depth: 3 // Default max depth
  });

  useEffect(() => {
    fetchQueueItems();
  }, [pendingPage, pendingRowsPerPage, processingPage, processingRowsPerPage, processedPage, processedRowsPerPage, tabValue]);

  // Initial load to get counts for all tabs
  useEffect(() => {
    fetchAllCounts();
  }, []);

  // Fetch counts for all tabs without fetching the actual items
  const fetchAllCounts = async () => {
    try {
      const [pendingRes, processingRes, processedRes] = await Promise.all([
        apiService.getQueuePending({ page: 1, limit: 1 }),
        apiService.getQueueProcessing({ page: 1, limit: 1 }),
        apiService.getQueueProcessed({ page: 1, limit: 1 })
      ]);

      setPendingTotal(pendingRes.data.total_count);
      setProcessingTotal(processingRes.data.total_count);
      setProcessedTotal(processedRes.data.total_count);
    } catch (error) {
      console.error('Error fetching counts:', error);
    }
  };

  const fetchQueueItems = async () => {
    try {
      setLoading(true);

      // Prepare pagination parameters for each tab
      const pendingParams = { page: pendingPage + 1, limit: pendingRowsPerPage };
      const processingParams = { page: processingPage + 1, limit: processingRowsPerPage };
      const processedParams = { page: processedPage + 1, limit: processedRowsPerPage };

      // Only fetch data for the active tab to improve performance
      if (tabValue === 0) {
        const pendingRes = await apiService.getQueuePending(pendingParams);
        setPendingItems(pendingRes.data.data);
        setPendingTotal(pendingRes.data.total_count);
      } else if (tabValue === 1) {
        const processingRes = await apiService.getQueueProcessing(processingParams);
        setProcessingItems(processingRes.data.data);
        setProcessingTotal(processingRes.data.total_count);
      } else if (tabValue === 2) {
        const processedRes = await apiService.getQueueProcessed(processedParams);
        setProcessedItems(processedRes.data.data);
        setProcessedTotal(processedRes.data.total_count);
      }
    } catch (error) {
      console.error('Error fetching queue items:', error);
      enqueueSnackbar('Failed to fetch queue items', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleChangePage = (_event: unknown, newValue: number) => {
    switch (tabValue) {
      case 0:
        setPendingPage(newValue);
        break;
      case 1:
        setProcessingPage(newValue);
        break;
      case 2:
        setProcessedPage(newValue);
        break;
    }
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    switch (tabValue) {
      case 0:
        setPendingRowsPerPage(newRowsPerPage);
        setPendingPage(0);
        break;
      case 1:
        setProcessingRowsPerPage(newRowsPerPage);
        setProcessingPage(0);
        break;
      case 2:
        setProcessedRowsPerPage(newRowsPerPage);
        setProcessedPage(0);
        break;
    }
  };

  const handleViewDetails = (item: QueueItem) => {
    setSelectedItem(item);
    setDetailsOpen(true);
  };

  const handleDeleteClick = (item: QueueItem) => {
    setSelectedItem(item);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedItem) return;

    try {
      setLoading(true);
      await apiService.removeQueueItem(selectedItem.id);
      enqueueSnackbar('Queue item removed successfully', { variant: 'success' });
      fetchQueueItems();
      fetchAllCounts(); // Refresh the counts for all tabs
    } catch (error) {
      console.error('Error removing queue item:', error);
      enqueueSnackbar('Failed to remove queue item', { variant: 'error' });
    } finally {
      setLoading(false);
      setDeleteDialogOpen(false);
    }
  };

  // Get template data based on data type
  const getTemplateData = (dataType: string): string => {
    switch (dataType) {
      case 'domain':
        return 'example.com';
      case 'ip':
        return '***********';
      case 'iprange':
        return '***********/24';
      case 'asn':
        return 'AS15169';
      case 'email':
        return '<EMAIL>';
      case 'org':
        return 'Example Organization';
      default:
        return '';
    }
  };



  const handleAddItem = async () => {
    try {
      // Check if data is empty
      if (!newItem.data.trim()) {
        enqueueSnackbar('Data cannot be empty', { variant: 'error' });
        return;
      }

      setLoading(true);
      console.log('Adding queue item:', newItem);

      // Create the payload
      const payload: {
        data_type: string;
        data: string;
        parent_id?: string;
        max_depth?: number;
      } = {
        data_type: newItem.data_type,
        data: newItem.data, // Send as string
        max_depth: newItem.max_depth
      };

      // Add parent_id only if it's not empty
      if (newItem.parent_id && newItem.parent_id.trim() !== '') {
        payload.parent_id = newItem.parent_id;
      }

      console.log('Sending payload:', payload);
      const response = await apiService.addQueueItem(payload);
      console.log('Response:', response);

      enqueueSnackbar('Queue item added successfully', { variant: 'success' });
      setAddDialogOpen(false);
      setNewItem({
        data_type: 'domain',
        parent_id: '',
        data: getTemplateData('domain'),
        max_depth: 3
      });
      fetchQueueItems(); // Refresh the queue items
      fetchAllCounts(); // Refresh the counts for all tabs
    } catch (error) {
      console.error('Error adding queue item:', error);
      enqueueSnackbar('Failed to add queue item', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusChipColor = (status: string) => {
    switch (status) {
      case 'pending': return 'default';
      case 'processing': return 'primary';
      case 'completed': return 'success';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const renderQueueItemData = (data: any) => {
    if (!data) return 'No data';

    // Display data as a string without attempting to parse as JSON
    return (
      <Box sx={{ maxHeight: '300px', overflow: 'auto' }}>
        <pre style={{ margin: 0 }}>{typeof data === 'string' ? data : String(data)}</pre>
      </Box>
    );
  };



  return (
    <Layout title="Queue">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Queue Management
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          View and manage queue items for the crawler.
        </Typography>
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={() => {
            fetchQueueItems();
            fetchAllCounts();
          }}
          disabled={loading}
        >
          Refresh
        </Button>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => setAddDialogOpen(true)}
        >
          Add Queue Item
        </Button>
      </Box>

      <Paper sx={{ width: '100%' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          centered
        >
          <Tab label={`Pending (${pendingTotal})`} />
          <Tab label={`Processing (${processingTotal})`} />
          <Tab label={`Processed (${processedTotal})`} />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Depth</TableCell>
                  <TableCell>Created At</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : pendingItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      No pending items
                    </TableCell>
                  </TableRow>
                ) : (
                  pendingItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.id.substring(0, 8)}...</TableCell>
                        <TableCell>{item.data_type}</TableCell>
                        <TableCell>
                          <Chip
                            label={item.status}
                            color={getStatusChipColor(item.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {item.current_depth !== undefined && item.max_depth !== undefined ?
                            `${item.current_depth}/${item.max_depth}` :
                            item.max_depth !== undefined ?
                              `0/${item.max_depth}` :
                              'N/A'}
                        </TableCell>
                        <TableCell>{formatDate(item.created_at)}</TableCell>
                        <TableCell>
                          <IconButton
                            color="primary"
                            onClick={() => handleViewDetails(item)}
                            title="View Details"
                          >
                            <VisibilityIcon />
                          </IconButton>
                          <IconButton
                            color="error"
                            onClick={() => handleDeleteClick(item)}
                            title="Remove from Queue"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                )}
              </TableBody>
            </Table>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50, 100]}
              component="div"
              count={pendingTotal}
              rowsPerPage={pendingRowsPerPage}
              page={pendingPage}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Depth</TableCell>
                  <TableCell>Attempts</TableCell>
                  <TableCell>Last Attempt</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : processingItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      No processing items
                    </TableCell>
                  </TableRow>
                ) : (
                  processingItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.id.substring(0, 8)}...</TableCell>
                        <TableCell>{item.data_type}</TableCell>
                        <TableCell>
                          <Chip
                            label={item.status}
                            color={getStatusChipColor(item.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {item.current_depth !== undefined && item.max_depth !== undefined ?
                            `${item.current_depth}/${item.max_depth}` :
                            item.max_depth !== undefined ?
                              `0/${item.max_depth}` :
                              'N/A'}
                        </TableCell>
                        <TableCell>{item.attempts}</TableCell>
                        <TableCell>
                          {item.last_attempt ? formatDate(item.last_attempt) : 'N/A'}
                        </TableCell>
                        <TableCell>
                          <IconButton
                            color="primary"
                            onClick={() => handleViewDetails(item)}
                            title="View Details"
                          >
                            <VisibilityIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                )}
              </TableBody>
            </Table>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50, 100]}
              component="div"
              count={processingTotal}
              rowsPerPage={processingRowsPerPage}
              page={processingPage}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Depth</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Completed</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading && processedItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <CircularProgress size={24} />
                    </TableCell>
                  </TableRow>
                ) : processedItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      No processed items
                    </TableCell>
                  </TableRow>
                ) : (
                  processedItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.id.substring(0, 8)}...</TableCell>
                        <TableCell>{item.data_type}</TableCell>
                        <TableCell>
                          <Chip
                            label={item.status}
                            color={getStatusChipColor(item.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {item.current_depth !== undefined && item.max_depth !== undefined ?
                            `${item.current_depth}/${item.max_depth}` :
                            item.max_depth !== undefined ?
                              `0/${item.max_depth}` :
                              'N/A'}
                        </TableCell>
                        <TableCell>{formatDate(item.created_at)}</TableCell>
                        <TableCell>{formatDate(item.updated_at)}</TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            onClick={() => handleViewDetails(item)}
                          >
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                )}
              </TableBody>
            </Table>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50, 100]}
              component="div"
              count={processedTotal}
              rowsPerPage={processedRowsPerPage}
              page={processedPage}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableContainer>
        </TabPanel>
      </Paper>

      {/* Queue Item Details Dialog */}
      <Dialog open={detailsOpen} onClose={() => setDetailsOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Queue Item Details</DialogTitle>
        <DialogContent dividers>
          {selectedItem && (
            <Box>
              <Typography variant="subtitle1">ID</Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>{selectedItem.id}</Typography>

              <Typography variant="subtitle1">Type</Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>{selectedItem.data_type}</Typography>

              <Typography variant="subtitle1">Status</Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                <Chip
                  label={selectedItem.status}
                  color={getStatusChipColor(selectedItem.status) as any}
                  size="small"
                />
              </Typography>

              <Typography variant="subtitle1">Parent ID</Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                {selectedItem.parent_id || 'None'}
              </Typography>

              <Typography variant="subtitle1">Created At</Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                {formatDate(selectedItem.created_at)}
              </Typography>

              <Typography variant="subtitle1">Updated At</Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                {formatDate(selectedItem.updated_at)}
              </Typography>

              <Typography variant="subtitle1">Depth</Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                {selectedItem.current_depth !== undefined && selectedItem.max_depth !== undefined ?
                  `${selectedItem.current_depth}/${selectedItem.max_depth}` :
                  selectedItem.max_depth !== undefined ?
                    `0/${selectedItem.max_depth}` :
                    'N/A'}
              </Typography>

              <Typography variant="subtitle1">Attempts</Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                {selectedItem.attempts}
              </Typography>

              {selectedItem.last_attempt && (
                <>
                  <Typography variant="subtitle1">Last Attempt</Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    {formatDate(selectedItem.last_attempt)}
                  </Typography>
                </>
              )}

              {selectedItem.error_message && (
                <>
                  <Typography variant="subtitle1">Error Message</Typography>
                  <Typography variant="body2" color="error" sx={{ mb: 2 }}>
                    {selectedItem.error_message}
                  </Typography>
                </>
              )}

              <Typography variant="subtitle1">Data</Typography>
              {renderQueueItemData(selectedItem.data)}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Remove Queue Item</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to remove this queue item? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            {loading ? <CircularProgress size={24} /> : 'Remove'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Queue Item Dialog */}
      <Dialog open={addDialogOpen} onClose={() => setAddDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add Queue Item</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth required>
                <InputLabel>Data Type</InputLabel>
                <Select
                  value={newItem.data_type}
                  onChange={(e) => setNewItem({ ...newItem, data_type: e.target.value, data: getTemplateData(e.target.value) })}
                  label="Data Type"
                >
                  <MenuItem value="domain">Domain</MenuItem>
                  <MenuItem value="ip">IP Address</MenuItem>
                  <MenuItem value="iprange">IP Range</MenuItem>
                  <MenuItem value="asn">ASN</MenuItem>
                  <MenuItem value="email">Email</MenuItem>
                  <MenuItem value="org">Organization</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Parent ID (Optional)"
                value={newItem.parent_id}
                onChange={(e) => setNewItem({ ...newItem, parent_id: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Max Depth"
                type="number"
                value={newItem.max_depth}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  if (!isNaN(value) && value >= 0) {
                    setNewItem({ ...newItem, max_depth: value });
                  }
                }}
                inputProps={{ min: 0 }}
                helperText="Default is 3"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Data (String)"
                value={newItem.data}
                onChange={(e) => setNewItem({ ...newItem, data: e.target.value })}
                multiline
                rows={10}
                required
                helperText="Enter the data as a plain string, not JSON"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleAddItem}
            color="primary"
            disabled={!newItem.data_type || !newItem.data || loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default QueuePage;
