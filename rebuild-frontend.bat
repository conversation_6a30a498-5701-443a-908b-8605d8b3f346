@echo off
setlocal enabledelayedexpansion

REM Script to rebuild the frontend container

REM Display usage information
if "%~1"=="" (
  echo Usage: %0 [option]
  echo Options:
  echo   dev       - Rebuild frontend for development environment
  echo   prod      - Rebuild frontend for production environment
  echo   help      - Show this help message
  exit /b 1
)

REM Process the option
if "%~1"=="dev" (
  echo Rebuilding frontend for development environment...
  echo Stopping frontend containers...
  docker-compose -f docker-compose.yml -f docker-compose.dev.yml stop frontend frontend-build
  
  echo Rebuilding frontend containers...
  docker-compose -f docker-compose.yml -f docker-compose.dev.yml build frontend-build frontend
  
  echo Starting frontend containers...
  docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d frontend-build frontend
  
  echo Frontend containers rebuilt and started for development environment.
) else if "%~1"=="prod" (
  echo Rebuilding frontend for production environment...
  echo Stopping frontend container...
  docker-compose -f docker-compose.yml -f docker-compose.prod.yml stop frontend
  
  echo Rebuilding frontend container...
  docker-compose -f docker-compose.yml -f docker-compose.prod.yml build frontend
  
  echo Starting frontend container...
  docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d frontend
  
  echo Frontend container rebuilt and started for production environment.
) else if "%~1"=="help" (
  echo Usage: %0 [option]
  echo Options:
  echo   dev       - Rebuild frontend for development environment
  echo   prod      - Rebuild frontend for production environment
  echo   help      - Show this help message
  exit /b 0
) else (
  echo Error: Unknown option '%~1'
  echo Usage: %0 [option]
  echo Options:
  echo   dev       - Rebuild frontend for development environment
  echo   prod      - Rebuild frontend for production environment
  echo   help      - Show this help message
  exit /b 1
)
