package api

import (
	"fmt"

	"github.com/crawler/db"
	"github.com/crawler/models"
)

// GraphService handles operations related to graph data
type GraphService struct{}

// NewGraphService creates a new graph service
func NewGraphService() *GraphService {
	return &GraphService{}
}

// GetGraphData retrieves entities and relationships and converts them to GraphData
func (s *GraphService) GetGraphData(filters map[string]interface{}, depth int) (*models.GraphData, error) {
	// Initialize graph data
	graphData := &models.GraphData{
		Nodes: []models.GraphNode{},
		Links: []models.GraphLink{},
	}

	// Track processed entities to avoid duplicates
	processedEntities := make(map[string]bool)
	processedLinks := make(map[string]bool)

	// Get root entities based on filters
	var rootEntities []models.Entity
	query := db.DB.Model(&models.Entity{})

	// Apply filters
	for key, value := range filters {
		if key == "id" {
			query = query.Where("id = ?", value)
		} else if key == "asset_type" {
			query = query.Where("asset_type = ?", value)
		} else if key == "asset_name" {
			query = query.Where("asset_name LIKE ?", fmt.Sprintf("%%%v%%", value))
		} else if key == "parent_id" {
			if value == nil {
				query = query.Where("parent_id IS NULL")
			} else {
				query = query.Where("parent_id = ?", value)
			}
		}
	}

	// Execute query
	if err := query.Find(&rootEntities).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch root entities: %w", err)
	}

	// Process each root entity
	for _, entity := range rootEntities {
		if err := s.processEntity(entity, graphData, processedEntities, processedLinks, depth, 0); err != nil {
			return nil, err
		}
	}

	return graphData, nil
}

// processEntity recursively processes an entity and its relationships
func (s *GraphService) processEntity(
	entity models.Entity,
	graphData *models.GraphData,
	processedEntities map[string]bool,
	processedLinks map[string]bool,
	maxDepth int,
	currentDepth int,
) error {
	// Check if we've already processed this entity
	if processedEntities[entity.ID] {
		return nil
	}

	// Mark as processed
	processedEntities[entity.ID] = true

	// Create graph node
	node := models.GraphNode{
		ID:         entity.ID,
		Name:       entity.AssetName,
		Type:       entity.AssetType,
		Attributes: entity.Attributes,
	}

	// Add node to graph data
	graphData.Nodes = append(graphData.Nodes, node)

	// Stop recursion if we've reached max depth
	if maxDepth > 0 && currentDepth >= maxDepth {
		return nil
	}

	// Process parent relationship (hierarchy)
	if entity.ParentID != nil {
		// Create link key to avoid duplicates
		linkKey := fmt.Sprintf("hierarchy:%s:%s", *entity.ParentID, entity.ID)
		if !processedLinks[linkKey] {
			// Add link to graph data
			link := models.GraphLink{
				Source:   *entity.ParentID,
				Target:   entity.ID,
				LinkType: "hierarchy",
				Label:    "parent-child",
			}
			graphData.Links = append(graphData.Links, link)
			processedLinks[linkKey] = true

			// Fetch and process parent if not already processed
			if !processedEntities[*entity.ParentID] {
				var parent models.Entity
				if err := db.DB.First(&parent, "id = ?", *entity.ParentID).Error; err == nil {
					if err := s.processEntity(parent, graphData, processedEntities, processedLinks, maxDepth, currentDepth+1); err != nil {
						return err
					}
				}
			}
		}
	}

	// Process children (hierarchy)
	var children []models.Entity
	if err := db.DB.Find(&children, "parent_id = ?", entity.ID).Error; err == nil {
		for _, child := range children {
			// Create link key to avoid duplicates
			linkKey := fmt.Sprintf("hierarchy:%s:%s", entity.ID, child.ID)
			if !processedLinks[linkKey] {
				// Add link to graph data
				link := models.GraphLink{
					Source:   entity.ID,
					Target:   child.ID,
					LinkType: "hierarchy",
					Label:    "parent-child",
				}
				graphData.Links = append(graphData.Links, link)
				processedLinks[linkKey] = true
			}

			// Process child recursively
			if err := s.processEntity(child, graphData, processedEntities, processedLinks, maxDepth, currentDepth+1); err != nil {
				return err
			}
		}
	}

	// Process relationships (cross-references)
	var relationships []models.EntityRelationship
	if err := db.DB.Find(&relationships, "from_entity_id = ?", entity.ID).Error; err == nil {
		for _, rel := range relationships {
			// Create link key to avoid duplicates
			linkKey := fmt.Sprintf("relationship:%s:%s:%s", rel.FromEntityID, rel.ToEntityID, rel.Label)
			if !processedLinks[linkKey] {
				// Add link to graph data
				link := models.GraphLink{
					Source:   rel.FromEntityID,
					Target:   rel.ToEntityID,
					Label:    rel.Label,
					Color:    rel.Color,
					LinkType: "cross-reference",
				}
				graphData.Links = append(graphData.Links, link)
				processedLinks[linkKey] = true
			}

			// Process target entity if not already processed
			if !processedEntities[rel.ToEntityID] {
				var targetEntity models.Entity
				if err := db.DB.First(&targetEntity, "id = ?", rel.ToEntityID).Error; err == nil {
					if err := s.processEntity(targetEntity, graphData, processedEntities, processedLinks, maxDepth, currentDepth+1); err != nil {
						return err
					}
				}
			}
		}
	}

	// Process incoming relationships
	if err := db.DB.Find(&relationships, "to_entity_id = ?", entity.ID).Error; err == nil {
		for _, rel := range relationships {
			// Create link key to avoid duplicates
			linkKey := fmt.Sprintf("relationship:%s:%s:%s", rel.FromEntityID, rel.ToEntityID, rel.Label)
			if !processedLinks[linkKey] {
				// Add link to graph data
				link := models.GraphLink{
					Source:   rel.FromEntityID,
					Target:   rel.ToEntityID,
					Label:    rel.Label,
					Color:    rel.Color,
					LinkType: "cross-reference",
				}
				graphData.Links = append(graphData.Links, link)
				processedLinks[linkKey] = true
			}

			// Process source entity if not already processed
			if !processedEntities[rel.FromEntityID] {
				var sourceEntity models.Entity
				if err := db.DB.First(&sourceEntity, "id = ?", rel.FromEntityID).Error; err == nil {
					if err := s.processEntity(sourceEntity, graphData, processedEntities, processedLinks, maxDepth, currentDepth+1); err != nil {
						return err
					}
				}
			}
		}
	}

	return nil
}

// GetEntityWithRelationships retrieves a single entity with all its relationships
func (s *GraphService) GetEntityWithRelationships(entityID string) (*models.GraphData, error) {
	filters := map[string]interface{}{
		"id": entityID,
	}
	return s.GetGraphData(filters, 1) // Depth 1 to get immediate relationships
}

// GetEntitiesByType retrieves entities of a specific type with their relationships
func (s *GraphService) GetEntitiesByType(assetType string) (*models.GraphData, error) {
	filters := map[string]interface{}{
		"asset_type": assetType,
	}
	return s.GetGraphData(filters, 1) // Depth 1 to get immediate relationships
}

// GetRootEntities retrieves entities with no parent
func (s *GraphService) GetRootEntities() (*models.GraphData, error) {
	filters := map[string]interface{}{
		"parent_id": nil,
	}
	return s.GetGraphData(filters, 1) // Depth 1 to get immediate children
}
