package api

import (
	"net/http"
	"strconv"

	"github.com/crawler/models"
	"github.com/gin-gonic/gin"
)

// GraphHandler handles graph-related API endpoints
type GraphHandler struct {
	graphService GraphServiceInterface
}

// GraphServiceInterface defines the methods needed by the graph handler
type GraphServiceInterface interface {
	GetGraphData(filters map[string]interface{}, depth int) (*models.GraphData, error)
	GetEntityWithRelationships(entityID string) (*models.GraphData, error)
	GetEntitiesByType(assetType string) (*models.GraphData, error)
	GetRootEntities() (*models.GraphData, error)
}

// NewGraphHandler creates a new graph handler
func NewGraphHandler() *GraphHandler {
	// Use PostgreSQL-based graph service for better performance
	return &GraphHandler{
		graphService: NewPostgresGraphService(),
	}
}

// GetGraphData returns graph data for visualization
func (h *<PERSON><PERSON>h<PERSON><PERSON><PERSON>) GetGraphData(c *gin.Context) {
	// Parse query parameters
	filters := make(map[string]interface{})

	// Handle specific filters
	if id := c.Query("id"); id != "" {
		filters["id"] = id
	}

	if assetType := c.Query("asset_type"); assetType != "" {
		filters["asset_type"] = assetType
	}

	if assetName := c.Query("asset_name"); assetName != "" {
		filters["asset_name"] = assetName
	}

	if parentID := c.Query("parent_id"); parentID != "" {
		if parentID == "null" {
			filters["parent_id"] = nil
		} else {
			filters["parent_id"] = parentID
		}
	}

	// Parse depth parameter
	depth := 1 // Default depth
	if depthStr := c.Query("depth"); depthStr != "" {
		if parsedDepth, err := strconv.Atoi(depthStr); err == nil {
			depth = parsedDepth
		}
	}

	// Get graph data
	graphData, err := h.graphService.GetGraphData(filters, depth)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, graphData)
}

// GetEntityGraph returns graph data for a specific entity
func (h *GraphHandler) GetEntityGraph(c *gin.Context) {
	entityID := c.Param("id")
	if entityID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Entity ID is required"})
		return
	}

	graphData, err := h.graphService.GetEntityWithRelationships(entityID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, graphData)
}

// GetEntitiesByTypeGraph returns graph data for entities of a specific type
func (h *GraphHandler) GetEntitiesByTypeGraph(c *gin.Context) {
	assetType := c.Param("type")
	if assetType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Asset type is required"})
		return
	}

	graphData, err := h.graphService.GetEntitiesByType(assetType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, graphData)
}

// GetRootEntitiesGraph returns graph data for root entities (entities with no parent)
func (h *GraphHandler) GetRootEntitiesGraph(c *gin.Context) {
	graphData, err := h.graphService.GetRootEntities()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, graphData)
}
