version: '3.8'

services:
  # Development-specific overrides
  
  # Add development-specific environment variables to the API service
  api:
    environment:
      - GIN_MODE=debug
    # For development, we can expose the API directly for debugging
    ports:
      - "${API_PORT:-8080}:${API_PORT:-8080}"
  
  # Use the multi-stage build approach with volumes for development
  frontend-build:
    build:
      context: ./frontend
      dockerfile: Dockerfile.multistage
    container_name: crawler-frontend-build
    volumes:
      - frontend_build:/output
    environment:
      REACT_APP_API_URL: ${REACT_APP_API_URL:-/api}

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.nginx.volume
    container_name: crawler-frontend
    depends_on:
      - nginx-api
      - frontend-build
    ports:
      - "80:80"
    volumes:
      - frontend_build:/usr/share/nginx/html
    environment:
      - REACT_APP_API_URL=/api
    
  # Add development-specific configuration to the nginx-api service
  nginx-api:
    volumes:
      - ./nginx/api.conf:/etc/nginx/conf.d/default.conf
    # Enable Nginx debug logging
    command: ["nginx-debug", "-g", "daemon off;"]

volumes:
  frontend_build:
