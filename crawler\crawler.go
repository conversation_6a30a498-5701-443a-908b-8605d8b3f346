package crawler

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/crawler/db"
	"github.com/crawler/models"
	"github.com/crawler/queue"
	"github.com/google/uuid"
)

// Crawler manages the crawling process
type Crawler struct {
	queueManager     *queue.QueueManager
	processors       map[string]DataProcessor
	visitedCache     sync.Map        // Thread-safe map to track visited entities
	blacklistCache   sync.Map        // Thread-safe map to track blacklisted entities
	manualCrawlTypes map[string]bool // Map of entity types that should only be crawled manually
	mu               sync.Mutex      // Mutex for thread-safe operations
	recoveryStopCh   chan struct{}   // Channel to signal the recovery goroutine to stop
}

// DataProcessor is an interface for processing different types of data
type DataProcessor interface {
	Process(item *models.QueueItem) ([]*models.QueueItem, *models.Entity, []*models.EntityRelationship, error)
}

// NewCrawler creates a new crawler instance
func NewCrawler(workerCount int, manualCrawlTypes []string) *Crawler {
	// Create a map for faster lookups of manual crawl types
	manualCrawlMap := make(map[string]bool)
	for _, dataType := range manualCrawlTypes {
		manualCrawlMap[dataType] = true
	}

	crawler := &Crawler{
		queueManager:     queue.NewQueueManager(workerCount),
		processors:       make(map[string]DataProcessor),
		manualCrawlTypes: manualCrawlMap,
		recoveryStopCh:   make(chan struct{}),
	}

	// Initialize the visited cache from the database
	crawler.initVisitedCache()
	// Initialize the blacklist cache from the database
	crawler.initBlacklistCache()

	return crawler
}

// initVisitedCache initializes the visited cache from the database
func (c *Crawler) initVisitedCache() {
	// This is called when the crawler starts
	// It loads all existing entities from the database into the visited cache
	var entities []models.Entity

	// Only load IDs to save memory
	if err := db.DB.Model(&models.Entity{}).Select("id").Find(&entities).Error; err == nil {
		for _, entity := range entities {
			c.visitedCache.Store(entity.ID, true)
		}
	}
}

// initBlacklistCache initializes the blacklist cache from the database
func (c *Crawler) initBlacklistCache() {
	// Load all blacklisted entities from the database into the blacklist cache
	var blacklistedEntities []models.BlacklistedEntity

	// Load all blacklisted entities
	if err := db.DB.Find(&blacklistedEntities).Error; err == nil {
		for _, entity := range blacklistedEntities {
			// Store by ID for exact matches
			c.blacklistCache.Store(entity.ID, true)

			// Store by type and name for pattern matching
			if entity.AssetType != "" && entity.AssetName != "" {
				key := fmt.Sprintf("%s:%s", entity.AssetType, entity.AssetName)
				c.blacklistCache.Store(key, true)
			}

			// Store by type for type-wide blacklisting
			if entity.AssetType != "" && entity.AssetName == "" {
				c.blacklistCache.Store("type:"+entity.AssetType, true)
			}

			// Store pattern-based blacklist entries
			if entity.Pattern != "" {
				// We store the pattern with a special prefix to distinguish it
				key := fmt.Sprintf("pattern:%s:%s", entity.AssetType, entity.Pattern)
				c.blacklistCache.Store(key, true)
			}
		}
	}
}

// RegisterProcessor registers a data processor for a specific data type
func (c *Crawler) RegisterProcessor(dataType string, processor DataProcessor) {
	c.processors[dataType] = processor
}

// Start begins the crawling process
func (c *Crawler) Start() error {
	// Refresh the visited cache from the database before starting
	c.initVisitedCache()
	// Refresh the blacklist cache from the database before starting
	c.initBlacklistCache()

	// Start a goroutine to periodically recover stuck items
	go c.startStuckItemRecovery()

	// Start processing the queue
	return c.queueManager.StartProcessing(c.processItem)
}

// startStuckItemRecovery starts a goroutine that periodically checks for and recovers stuck items
func (c *Crawler) startStuckItemRecovery() {
	// Check for stuck items every 15 minutes
	ticker := time.NewTicker(15 * time.Minute)
	defer ticker.Stop()

	// Consider an item stuck if it's been processing for more than 30 minutes
	maxProcessingTime := 30 * time.Minute

	for {
		select {
		case <-c.recoveryStopCh:
			// Stop the recovery goroutine
			fmt.Println("Stopping stuck item recovery goroutine...")
			return
		case <-ticker.C:
			// Recover items that have been stuck in processing state for too long
			count, err := c.queueManager.RecoverStuckItems(maxProcessingTime)
			if err != nil {
				fmt.Printf("Error recovering stuck items: %v\n", err)
			} else if count > 0 {
				fmt.Printf("Recovered %d stuck items\n", count)
			}
		}
	}
}

// RefreshBlacklistCache refreshes the blacklist cache from the database
func (c *Crawler) RefreshBlacklistCache() {
	// Clear the existing cache
	c.blacklistCache = sync.Map{}
	// Reload from database
	c.initBlacklistCache()
}

// Stop stops the crawling process
func (c *Crawler) Stop() error {
	// Stop the recovery goroutine
	close(c.recoveryStopCh)

	// Stop the queue processing
	return c.queueManager.StopProcessing()
}

// GetStatus returns the current status of the crawler
func (c *Crawler) GetStatus() (string, string) {
	return c.queueManager.GetStatus()
}

// IsManualCrawlOnly checks if an entity type should only be crawled manually
func (c *Crawler) IsManualCrawlOnly(dataType string) bool {
	return c.manualCrawlTypes[dataType]
}

// AddToQueue adds a new item to the crawl queue
func (c *Crawler) AddToQueue(item *models.QueueItem) error {
	// Check if this item's data type is blacklisted
	if c.isEntityBlacklisted(item.DataType, "") {
		return fmt.Errorf("data type %s is blacklisted", item.DataType)
	}

	// Note: We allow manual crawling of manual-only types, so we don't check for manual-only types here

	// If this is a requeue operation, use the force method
	if item.IsRequeue {
		return c.queueManager.AddItem(item) // We'll handle requeue logic in processItem
	}

	// Extract data from the queue item to check for duplicates in the queue
	var data map[string]interface{}
	if err := item.GetData(&data); err == nil {
		// Check if there's already a similar item in the queue
		var count int64

		if id, ok := data["id"].(string); ok && id != "" {
			// Check by ID if available
			db.DB.Model(&models.QueueItem{}).Where(
				"data_type = ? AND status IN (?, ?) AND data->>'id' = ?",
				item.DataType, "pending", "processing", id,
			).Count(&count)

			if count > 0 {
				return fmt.Errorf("a queue item for entity ID %s of type %s already exists in the queue", id, item.DataType)
			}
		} else if name, ok := data["name"].(string); ok && name != "" {
			// Check by name if ID is not available
			db.DB.Model(&models.QueueItem{}).Where(
				"data_type = ? AND status IN (?, ?) AND data->>'name' = ?",
				item.DataType, "pending", "processing", name,
			).Count(&count)

			if count > 0 {
				return fmt.Errorf("a queue item for entity name %s of type %s already exists in the queue", name, item.DataType)
			}
		}
	}

	// Check if parent entity exists if parent ID is provided
	if item.ParentID != nil {
		var parentExists int64
		if err := db.DB.Model(&models.Entity{}).Where("id = ?", *item.ParentID).Count(&parentExists).Error; err != nil {
			return fmt.Errorf("failed to check parent entity: %w", err)
		}

		if parentExists == 0 {
			return fmt.Errorf("parent entity with ID %s does not exist", *item.ParentID)
		}
	}

	// If this is a requeue operation, skip the existence checks
	if item.IsRequeue {
		return c.queueManager.AddItem(item) // We'll handle requeue logic in processItem
	}

	// Continue checking for duplicates in the database
	// We're reusing the same data variable from above
	// Check if this item would create an entity that already exists
	if id, ok := data["id"].(string); ok && id != "" {
		if c.isEntityVisited(id) {
			return fmt.Errorf("entity with ID %s already exists", id)
		}
	} else if name, ok := data["name"].(string); ok && name != "" {
		// Check if this specific entity is blacklisted
		if c.isEntityBlacklisted(item.DataType, name) {
			return fmt.Errorf("entity %s of type %s is blacklisted", name, item.DataType)
		}

		// Check if an entity with this name and type already exists
		var count int64
		db.DB.Model(&models.Entity{}).Where(
			"asset_type = ? AND asset_name = ?",
			item.DataType, name,
		).Count(&count)

		if count > 0 {
			return fmt.Errorf("entity with name %s and type %s already exists", name, item.DataType)
		}
	}

	return c.queueManager.AddItem(item)
}

// RemoveFromQueue removes an item from the crawl queue
func (c *Crawler) RemoveFromQueue(id string) error {
	return c.queueManager.RemoveItem(id)
}

// GetPendingItems returns all pending items in the queue
func (c *Crawler) GetPendingItems() ([]models.QueueItem, error) {
	return c.queueManager.GetPendingItems()
}

// GetProcessingItems returns all items currently being processed
func (c *Crawler) GetProcessingItems() ([]models.QueueItem, error) {
	return c.queueManager.GetProcessingItems()
}

// GetProcessedItems returns all completed or failed items
func (c *Crawler) GetProcessedItems() ([]models.QueueItem, error) {
	return c.queueManager.GetProcessedItems()
}

// GetPendingItemsPaginated returns paginated pending items in the queue
func (c *Crawler) GetPendingItemsPaginated(page, limit int) ([]models.QueueItem, int64, error) {
	return c.queueManager.GetPendingItemsPaginated(page, limit)
}

// GetProcessingItemsPaginated returns paginated items currently being processed
func (c *Crawler) GetProcessingItemsPaginated(page, limit int) ([]models.QueueItem, int64, error) {
	return c.queueManager.GetProcessingItemsPaginated(page, limit)
}

// GetProcessedItemsPaginated returns paginated completed or failed items
func (c *Crawler) GetProcessedItemsPaginated(page, limit int) ([]models.QueueItem, int64, error) {
	return c.queueManager.GetProcessedItemsPaginated(page, limit)
}

// RequeueEntity creates a new queue item for an existing entity with default depth
func (c *Crawler) RequeueEntity(entityID string) error {
	return c.RequeueEntityWithDepth(entityID, 3) // Default depth of 3
}

// RequeueEntityWithDepth creates a new queue item for an existing entity with specified depth
// If the entity already exists, it will be crawled again and updated with any new information
func (c *Crawler) RequeueEntityWithDepth(entityID string, maxDepth int) error {
	// Find the entity in the database
	var entity models.Entity
	if err := db.DB.First(&entity, "id = ?", entityID).Error; err != nil {
		return fmt.Errorf("entity not found: %w", err)
	}

	// Create a queue item for the entity
	item := &models.QueueItem{
		ID:           uuid.New().String(),
		DataType:     entity.AssetType,
		Status:       "pending",
		MaxDepth:     maxDepth,
		CurrentDepth: 0,
		IsRequeue:    true, // Mark as a requeue operation
	}

	// Also requeue all children recursively
	go c.requeueAllChildren(entityID, maxDepth)

	// Set parent ID if entity has a parent and the parent exists
	if entity.ParentID != nil {
		// Check if parent entity exists
		var parentExists int64
		if err := db.DB.Model(&models.Entity{}).Where("id = ?", *entity.ParentID).Count(&parentExists).Error; err != nil {
			return fmt.Errorf("failed to check parent entity: %w", err)
		}

		// Only set parent ID if parent exists
		if parentExists > 0 {
			item.ParentID = entity.ParentID
		}
	}

	// Extract entity attributes to use as data
	var attributes map[string]interface{}
	if err := json.Unmarshal(entity.Attributes, &attributes); err != nil {
		return fmt.Errorf("failed to unmarshal entity attributes: %w", err)
	}

	// Add entity ID and name to the data
	data := map[string]interface{}{
		"id":   entity.ID,
		"name": entity.AssetName,
	}

	// Merge attributes into data
	for k, v := range attributes {
		data[k] = v
	}

	// Set the data in the queue item
	if err := item.SetData(data); err != nil {
		return fmt.Errorf("failed to set item data: %w", err)
	}

	// Add to queue
	return c.queueManager.AddItem(item)
}

// isEntityVisited checks if an entity has already been processed
func (c *Crawler) isEntityVisited(entityID string) bool {
	// First check the in-memory cache
	if _, exists := c.visitedCache.Load(entityID); exists {
		return true
	}

	// If not in cache, check the database
	var count int64
	db.DB.Model(&models.Entity{}).Where("id = ?", entityID).Count(&count)

	// If found in database, add to cache and return true
	if count > 0 {
		c.visitedCache.Store(entityID, true)
		return true
	}

	return false
}

// isEntityBlacklisted checks if an entity is blacklisted
func (c *Crawler) isEntityBlacklisted(entityType string, entityName string) bool {
	// Check if the entity type is blacklisted
	if _, exists := c.blacklistCache.Load("type:" + entityType); exists {
		return true
	}

	// Check if the specific entity is blacklisted by type and name
	if entityName != "" {
		// Check for exact match
		key := fmt.Sprintf("%s:%s", entityType, entityName)
		if _, exists := c.blacklistCache.Load(key); exists {
			return true
		}

		// Check for pattern matches in cache
		// This is a simplified approach - for complex patterns, we still need the database query
		var foundMatch bool
		c.blacklistCache.Range(func(k, v interface{}) bool {
			key, ok := k.(string)
			if !ok {
				return true // continue iteration
			}

			// Check if this is a pattern entry for the right asset type
			if strings.HasPrefix(key, fmt.Sprintf("pattern:%s:", entityType)) {
				// Extract the pattern
				pattern := strings.TrimPrefix(key, fmt.Sprintf("pattern:%s:", entityType))

				// Simple wildcard matching for common patterns
				if strings.HasPrefix(pattern, "%") && strings.HasSuffix(entityName, strings.TrimPrefix(pattern, "%")) {
					foundMatch = true
					return false // stop iteration, we found a match
				}
				if strings.HasSuffix(pattern, "%") && strings.HasPrefix(entityName, strings.TrimSuffix(pattern, "%")) {
					foundMatch = true
					return false // stop iteration, we found a match
				}
			}
			return true // continue iteration
		})

		if foundMatch {
			return true
		}
	}

	// If not in cache, check the database for more complex patterns
	var count int64

	// Check for exact match
	db.DB.Model(&models.BlacklistedEntity{}).Where(
		"asset_type = ? AND asset_name = ?",
		entityType, entityName,
	).Count(&count)

	if count > 0 {
		// Add to cache for future checks
		key := fmt.Sprintf("%s:%s", entityType, entityName)
		c.blacklistCache.Store(key, true)
		return true
	}

	// Check for type-wide blacklisting
	db.DB.Model(&models.BlacklistedEntity{}).Where(
		"asset_type = ? AND asset_name = ''",
		entityType,
	).Count(&count)

	if count > 0 {
		// Add to cache for future checks
		c.blacklistCache.Store("type:"+entityType, true)
		return true
	}

	// Check for pattern matching using SQL LIKE and our custom pattern matching
	if entityName != "" {
		// Get all blacklist entries for this asset type with patterns
		var blacklistEntries []models.BlacklistedEntity
		db.DB.Where("asset_type = ? AND pattern != ''", entityType).Find(&blacklistEntries)

		// Check each entry for pattern matches
		for _, entry := range blacklistEntries {
			if entry.MatchesPattern(entityName) {
				// We found a match
				return true
			}
		}

		// As a fallback, use direct SQL LIKE query for database-specific patterns
		db.DB.Model(&models.BlacklistedEntity{}).Where(
			"asset_type = ? AND pattern != '' AND ? LIKE pattern",
			entityType, entityName,
		).Count(&count)

		if count > 0 {
			// We don't add this to cache since it's a pattern match
			// and would require storing all possible matches
			return true
		}
	}

	return false
}

// processItem processes a queue item
func (c *Crawler) processItem(item *models.QueueItem) error {
	// Check if we have a processor for this data type
	processor, ok := c.processors[item.DataType]
	if !ok {
		return fmt.Errorf("no processor registered for data type: %s", item.DataType)
	}

	// Check if this item's data type is blacklisted
	if c.isEntityBlacklisted(item.DataType, "") {
		return fmt.Errorf("data type %s is blacklisted", item.DataType)
	}

	// For manual-only entity types, we only process them if they are explicitly requeued by the user
	// Otherwise, we just create the entity but don't crawl it
	if c.manualCrawlTypes[item.DataType] && !item.IsRequeue {
		// Extract entity data to create the entity without crawling
		var entityData map[string]interface{}
		if err := item.GetData(&entityData); err != nil {
			return fmt.Errorf("failed to parse item data: %w", err)
		}

		// Create a basic entity without crawling
		entity := &models.Entity{
			ID:        uuid.New().String(),
			AssetType: item.DataType,
			AssetName: entityData["name"].(string),
			ParentID:  item.ParentID,
		}

		// Set attributes
		if err := entity.SetAttributes(entityData); err != nil {
			return fmt.Errorf("failed to set entity attributes: %w", err)
		}

		// Save the entity
		if err := db.DB.Save(entity).Error; err != nil {
			return fmt.Errorf("failed to save entity: %w", err)
		}

		// Add to visited cache
		c.visitedCache.Store(entity.ID, true)

		// Create relationship with parent if parent ID is provided
		if item.ParentID != nil {
			relationship := &models.EntityRelationship{
				FromEntityID: *item.ParentID,
				ToEntityID:   entity.ID,
				Label:        "has",
			}

			// Save the relationship
			if err := db.DB.Save(relationship).Error; err != nil {
				return fmt.Errorf("failed to save relationship: %w", err)
			}
		}

		fmt.Printf("Created manual-only entity %s (Type: %s, Name: %s) without crawling\n",
			entity.ID, entity.AssetType, entity.AssetName)

		// Skip further processing
		return nil
	}

	// Extract entity name from the item data to check blacklist
	var data map[string]interface{}
	if err := item.GetData(&data); err == nil {
		if name, ok := data["name"].(string); ok && name != "" {
			// Check if this specific entity is blacklisted
			if c.isEntityBlacklisted(item.DataType, name) {
				return fmt.Errorf("entity %s of type %s is blacklisted", name, item.DataType)
			}
		}
	}

	// Process the item
	newItems, entity, relationships, err := processor.Process(item)
	if err != nil {
		return err
	}

	// Log the current depth for debugging
	fmt.Printf("Processing item %s at depth %d/%d\n", item.ID, item.CurrentDepth, item.MaxDepth)

	// Save the entity if it exists
	if entity != nil {
		// Check if this is a requeue operation or if the entity hasn't been processed before
		if item.IsRequeue || !c.isEntityVisited(entity.ID) {
			// Save or update the entity
			if err := db.DB.Save(entity).Error; err != nil {
				return fmt.Errorf("failed to save entity: %w", err)
			}

			// If this is a requeue operation, log that we're updating the entity
			if item.IsRequeue {
				fmt.Printf("Updated existing entity %s (Type: %s, Name: %s)\n",
					entity.ID, entity.AssetType, entity.AssetName)
			} else {
				// Add to visited cache for new entities
				c.visitedCache.Store(entity.ID, true)
			}
		}
	}

	// Save relationships
	for _, rel := range relationships {
		// Check if relationship already exists
		var count int64
		db.DB.Model(&models.EntityRelationship{}).Where(
			"from_entity_id = ? AND to_entity_id = ? AND label = ?",
			rel.FromEntityID, rel.ToEntityID, rel.Label,
		).Count(&count)

		if count == 0 {
			// Relationship doesn't exist, save it
			if err := db.DB.Save(rel).Error; err != nil {
				return fmt.Errorf("failed to save relationship: %w", err)
			}
		}
	}

	// Add new items to the queue, but only if the target entity doesn't exist yet and is not blacklisted
	// Also respect the MaxDepth setting and FollowChildren flag

	// Check if the parent entity has DoFollow set to false
	var parentDoFollow bool = true // Default to true if we can't find the parent
	if entity != nil {
		// If we have the entity from the processor, use it directly
		parentDoFollow = entity.DoFollow
	} else if item.ParentID != nil {
		// Otherwise, look up the parent entity in the database
		var parentEntity models.Entity
		if err := db.DB.First(&parentEntity, "id = ?", *item.ParentID).Error; err == nil {
			parentDoFollow = parentEntity.DoFollow
		}
	}

	// If parent has DoFollow=false and this is not a manual requeue, skip adding children
	if !parentDoFollow && !item.IsRequeue {
		fmt.Printf("Skipping adding children because parent entity has DoFollow=false\n")
		return nil
	}

	for _, newItem := range newItems {
		// Set the current depth based on the parent item
		newItem.CurrentDepth = item.CurrentDepth + 1

		// If MaxDepth is not set (0), use the parent's MaxDepth
		if newItem.MaxDepth == 0 {
			newItem.MaxDepth = item.MaxDepth
		}

		// Check if we've reached the maximum depth
		if newItem.CurrentDepth > newItem.MaxDepth {
			fmt.Printf("Skipping item because max depth reached: %d > %d\n", newItem.CurrentDepth, newItem.MaxDepth)
			continue
		}

		// Extract entity ID or other identifier from the item data
		var data map[string]interface{}
		if err := newItem.GetData(&data); err != nil {
			continue // Skip this item if we can't parse the data
		}

		// Check if this item would create an entity that already exists or is blacklisted
		skipItem := false

		// Check if the data type is blacklisted
		if c.isEntityBlacklisted(newItem.DataType, "") {
			skipItem = true
			continue
		}

		// For manual-only entity types, we only skip them during requeue operations
		// During normal crawling, we allow them to be processed
		if item.IsRequeue && c.manualCrawlTypes[newItem.DataType] {
			fmt.Printf("Skipping automatic requeuing of %s (type: %s) - manual crawl only\n",
				newItem.ID, newItem.DataType)
			skipItem = true
			continue
		}

		// If this is a requeue operation, don't skip even if the entity exists
		// But still respect manual-only types (which we've already checked above)
		if item.IsRequeue {
			// Mark child items as requeue operations too
			newItem.IsRequeue = true
			// Don't skip requeued items unless they're manual-only types
			if !skipItem {
				skipItem = false
			}
		} else {
			// For normal operations, check if entity already exists
			if id, ok := data["id"].(string); ok && id != "" {
				if c.isEntityVisited(id) {
					skipItem = true
				}
			} else if name, ok := data["name"].(string); ok && name != "" {
				// Check if this specific entity is blacklisted
				if c.isEntityBlacklisted(newItem.DataType, name) {
					skipItem = true
					continue
				}

				// Check if an entity with this name and type already exists
				var count int64
				db.DB.Model(&models.Entity{}).Where(
					"asset_type = ? AND asset_name = ?",
					newItem.DataType, name,
				).Count(&count)

				if count > 0 {
					skipItem = true
				}
			}
		}

		if !skipItem {
			// Check if parent entity exists if parent ID is provided
			if newItem.ParentID != nil {
				var parentExists int64
				if err := db.DB.Model(&models.Entity{}).Where("id = ?", *newItem.ParentID).Count(&parentExists).Error; err != nil {
					return fmt.Errorf("failed to check parent entity: %w", err)
				}

				if parentExists == 0 {
					// Skip this item if parent doesn't exist
					continue
				}
			}

			if err := c.queueManager.AddItem(newItem); err != nil {
				return fmt.Errorf("failed to add new item to queue: %w", err)
			}
		}
	}

	return nil
}

// CreateQueueItem creates a new queue item
func CreateQueueItem(parent *models.Entity, dataType string, data interface{}) (*models.QueueItem, error) {
	item := &models.QueueItem{
		ID:       uuid.New().String(),
		DataType: dataType,
		Status:   "pending",
	}

	// Set parent ID if parent exists
	if parent != nil {
		// Check if parent entity exists in the database
		var parentExists int64
		if err := db.DB.Model(&models.Entity{}).Where("id = ?", parent.ID).Count(&parentExists).Error; err != nil {
			return nil, fmt.Errorf("failed to check parent entity: %w", err)
		}

		if parentExists > 0 {
			item.ParentID = &parent.ID
		} else {
			return nil, fmt.Errorf("parent entity with ID %s does not exist", parent.ID)
		}
	}

	// Set data
	if err := item.SetData(data); err != nil {
		return nil, errors.New("failed to set item data")
	}

	return item, nil
}
