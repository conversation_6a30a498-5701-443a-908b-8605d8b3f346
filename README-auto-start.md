# Auto-Start Crawler Feature

This document explains the implementation of the Auto-Start Crawler feature in the Crawler System.

## Overview

The Auto-Start Crawler feature allows the crawler to start automatically when the application is launched, eliminating the need to manually start it via the API. This is particularly useful for production environments where you want the crawler to begin processing items immediately after deployment or restart.

## Implementation Details

### Configuration

A new configuration option `AutoStartCrawler` has been added to control this behavior:

```go
// Config holds the application configuration
type Config struct {
    DB               db.Config
    Port             string
    WorkerPool       int
    AutoStartCrawler bool
}
```

This option can be set using the environment variable `AUTO_START_CRAWLER`:

```bash
# Enable auto-start (default)
AUTO_START_CRAWLER=true

# Disable auto-start
AUTO_START_CRAWLER=false
```

### Startup Process

The crawler is now automatically started during application initialization if the `AutoStartCrawler` option is enabled:

```go
// Auto-start crawler if configured
if cfg.AutoStartCrawler {
    log.Println("Auto-starting crawler...")
    if err := crawlerInstance.Start(); err != nil {
        log.Printf("Warning: Failed to auto-start crawler: %v", err)
    } else {
        log.Println("Crawler started successfully")
    }
}
```

If the auto-start fails for any reason, a warning is logged, but the application continues to start up. This ensures that the application remains operational even if there's an issue with the crawler.

## Benefits

1. **Improved User Experience**: No need to manually start the crawler after application startup
2. **Simplified Deployment**: Crawler begins processing immediately after deployment
3. **Reduced Downtime**: After a restart, the crawler automatically resumes processing
4. **Configurable Behavior**: Can be enabled or disabled based on environment needs

## Usage

### Default Behavior

By default, the crawler will auto-start when the application is launched. No additional configuration is needed.

### Disabling Auto-Start

If you prefer to manually start the crawler, you can disable the auto-start feature by setting the `AUTO_START_CRAWLER` environment variable to `false`:

```bash
# In your environment or .env file
AUTO_START_CRAWLER=false
```

### Verifying Crawler Status

You can verify the crawler's status through:

1. **API Endpoint**: `GET /api/crawler/status`
2. **Dashboard**: The crawler status is displayed on the dashboard
3. **Application Logs**: Look for "Crawler started successfully" in the logs

## Troubleshooting

If the crawler fails to auto-start, check the following:

1. **Database Connection**: Ensure the database is accessible
2. **Queue Items**: Verify there are items in the queue to process
3. **Processor Registration**: Confirm that processors are registered for all data types
4. **Application Logs**: Look for specific error messages in the logs
