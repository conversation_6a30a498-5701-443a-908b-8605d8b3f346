package models

import (
	"strings"
	"time"

	"github.com/google/uuid"
)

// BlacklistedEntity represents an entity that should not be crawled
type BlacklistedEntity struct {
	ID          string    `gorm:"primaryKey;type:text;column:id"`
	AssetType   string    `gorm:"column:asset_type;size:100;not null;index"`
	AssetName   string    `gorm:"column:asset_name;type:text;index"`
	Pattern     string    `gorm:"column:pattern;type:text"`
	Description string    `gorm:"column:description;type:text"`
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt   time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

// <PERSON><PERSON><PERSON> will set a UUID rather than numeric ID
func (b *BlacklistedEntity) BeforeCreate() error {
	if b.ID == "" {
		b.ID = uuid.New().String()
	}
	return nil
}

// TableName specifies the table name for GORM
func (BlacklistedEntity) TableName() string {
	return "blacklisted_entities"
}

// MatchesPattern checks if the given entity name matches this blacklist entry's pattern
func (b *BlacklistedEntity) MatchesPattern(entityName string) bool {
	// If no pattern is specified, only exact matches count
	if b.Pattern == "" {
		return b.AssetName == entityName
	}

	// For SQL LIKE patterns, we need to convert them to Go regex
	// This is a simplified implementation that handles common cases
	pattern := b.Pattern

	// Check for simple wildcard patterns
	if strings.HasPrefix(pattern, "%") && strings.HasSuffix(entityName, strings.TrimPrefix(pattern, "%")) {
		return true
	}
	if strings.HasSuffix(pattern, "%") && strings.HasPrefix(entityName, strings.TrimSuffix(pattern, "%")) {
		return true
	}

	// For more complex patterns, we could use regex
	// This would require converting SQL LIKE patterns to regex
	// For example: % -> .*, _ -> .

	return false
}
