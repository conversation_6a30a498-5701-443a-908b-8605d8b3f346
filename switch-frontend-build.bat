@echo off
setlocal enabledelayedexpansion

REM Script to switch between different frontend build Dockerfiles

REM Display usage information
if "%~1"=="" (
  echo Usage: %0 [option]
  echo Options:
  echo   bypass     - Use Dockerfile.bypass (completely bypasses TypeScript checking^)
  echo   nocheck    - Use Dockerfile.nocheck (skips TypeScript checking^)
  echo   nots       - Use Dockerfile.nots (removes TypeScript from build process^)
  echo   multistage - Use Dockerfile.multistage (multi-stage build with busybox^)
  echo   fix-grid   - Use Dockerfile.fix-grid (fixes Grid component issues^)
  echo   force      - Use Dockerfile.force (modifies package.json build script^)
  echo   help       - Show this help message
  exit /b 1
)

REM Process the option
set DOCKERFILE=
if "%~1"=="bypass" (
  set DOCKERFILE=Dockerfile.bypass
) else if "%~1"=="nocheck" (
  set DOCKERFILE=Dockerfile.nocheck
) else if "%~1"=="nots" (
  set DOCKERFILE=Dockerfile.nots
) else if "%~1"=="multistage" (
  set DOCKERFILE=Dockerfile.multistage
) else if "%~1"=="fix-grid" (
  set DOCKERFILE=Dockerfile.fix-grid
) else if "%~1"=="force" (
  set DOCKERFILE=Dockerfile.force
) else if "%~1"=="help" (
  echo Usage: %0 [option]
  echo Options:
  echo   bypass     - Use Dockerfile.bypass (completely bypasses TypeScript checking^)
  echo   nocheck    - Use Dockerfile.nocheck (skips TypeScript checking^)
  echo   nots       - Use Dockerfile.nots (removes TypeScript from build process^)
  echo   multistage - Use Dockerfile.multistage (multi-stage build with busybox^)
  echo   fix-grid   - Use Dockerfile.fix-grid (fixes Grid component issues^)
  echo   force      - Use Dockerfile.force (modifies package.json build script^)
  echo   help       - Show this help message
  exit /b 0
) else (
  echo Error: Unknown option '%~1'
  echo Usage: %0 [option]
  echo Options:
  echo   bypass     - Use Dockerfile.bypass (completely bypasses TypeScript checking^)
  echo   nocheck    - Use Dockerfile.nocheck (skips TypeScript checking^)
  echo   nots       - Use Dockerfile.nots (removes TypeScript from build process^)
  echo   multistage - Use Dockerfile.multistage (multi-stage build with busybox^)
  echo   fix-grid   - Use Dockerfile.fix-grid (fixes Grid component issues^)
  echo   force      - Use Dockerfile.force (modifies package.json build script^)
  echo   help       - Show this help message
  exit /b 1
)

REM Update the docker-compose.yml file
echo Switching frontend build to use %DOCKERFILE%...

REM Use PowerShell to perform the replacement
powershell -Command "(Get-Content docker-compose.yml) -replace 'dockerfile: Dockerfile\.[a-z-]*', 'dockerfile: %DOCKERFILE%' | Set-Content docker-compose.yml"

echo Done! The frontend build now uses %DOCKERFILE%.
echo To apply the changes, run: docker-compose up -d --build frontend-build
