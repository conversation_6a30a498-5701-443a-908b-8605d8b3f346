-- Function to get entity graph data
CREATE OR REPLACE FUNCTION get_entity_graph(
    p_filters JSONB,
    p_max_depth INT DEFAULT 1
)
RETURNS TEXT AS $$
DECLARE
    v_result JSONB;
BEGIN
    WITH RECURSIVE entity_graph AS (
        -- Base case: start with root entities based on filters
        SELECT
            e.id,
            e.asset_type,
            e.asset_name,
            e.parent_id,
            e.attributes,
            0 AS depth
        FROM
            entities e
        WHERE
            (p_filters->>'id' IS NULL OR e.id = p_filters->>'id') AND
            (p_filters->>'asset_type' IS NULL OR e.asset_type = p_filters->>'asset_type') AND
            (p_filters->>'asset_name' IS NULL OR e.asset_name LIKE '%' || (p_filters->>'asset_name') || '%') AND
            (
                (p_filters->>'parent_id_is_null' = 'true' AND e.parent_id IS NULL) OR
                (p_filters->>'parent_id' IS NOT NULL AND e.parent_id = p_filters->>'parent_id') OR
                (p_filters->>'parent_id' IS NULL AND p_filters->>'parent_id_is_null' IS NULL)
            )

        UNION

        -- Recursive case: get related entities up to max depth
        SELECT
            e.id,
            e.asset_type,
            e.asset_name,
            e.parent_id,
            e.attributes,
            eg.depth + 1
        FROM
            entities e
        JOIN
            entity_graph eg ON (
                -- Parent-child relationships (hierarchy)
                (e.parent_id = eg.id) OR
                (e.id = eg.parent_id) OR
                -- Cross-reference relationships
                EXISTS (
                    SELECT 1 FROM entity_relationships er
                    WHERE (er.from_entity_id = eg.id AND er.to_entity_id = e.id) OR
                          (er.to_entity_id = eg.id AND er.from_entity_id = e.id)
                )
            )
        WHERE
            eg.depth < p_max_depth
    ),

    -- Get all nodes for the graph
    nodes AS (
        SELECT DISTINCT
            jsonb_build_object(
                'id', id,
                'type', asset_type,
                'name', asset_name,
                'attributes', attributes
            ) AS node_data
        FROM
            entity_graph
    ),

    -- Get all hierarchy links
    hierarchy_links AS (
        SELECT DISTINCT
            jsonb_build_object(
                'source', e.parent_id,
                'target', e.id,
                'label', 'parent-child',
                'linkType', 'hierarchy'
            ) AS link_data
        FROM
            entity_graph e
        WHERE
            e.parent_id IS NOT NULL AND
            EXISTS (SELECT 1 FROM entity_graph eg WHERE eg.id = e.parent_id)
    ),

    -- Get all relationship links
    relationship_links AS (
        SELECT DISTINCT
            jsonb_build_object(
                'source', er.from_entity_id,
                'target', er.to_entity_id,
                'label', er.label,
                'color', er.color,
                'linkType', 'cross-reference'
            ) AS link_data
        FROM
            entity_relationships er
        JOIN
            entity_graph eg1 ON er.from_entity_id = eg1.id
        JOIN
            entity_graph eg2 ON er.to_entity_id = eg2.id
    )

    -- Build the final result
    SELECT
        jsonb_build_object(
            'nodes', COALESCE(jsonb_agg(n.node_data), '[]'::jsonb),
            'links', COALESCE(
                (
                    SELECT jsonb_agg(l.link_data)
                    FROM (
                        SELECT link_data FROM hierarchy_links
                        UNION ALL
                        SELECT link_data FROM relationship_links
                    ) l
                ),
                '[]'::jsonb
            )
        ) INTO v_result
    FROM
        nodes n;

    -- Convert JSONB to TEXT before returning
    RETURN v_result::TEXT;
END;
$$ LANGUAGE plpgsql;
