# Crawler System

A parallel processing crawler system with PostgreSQL database integration and queue persistence.

## Features

- Parallel processing of queue items
- PostgreSQL database for storing entities and relationships
- Queue persistence to handle service restarts
- RESTful API for system control and data retrieval
- Avoids circular loops and duplicate entity processing by checking both in-memory cache and database

## System Components

### Models

- **Entity**: Represents a node in the system with attributes and relationships
- **EntityRelationship**: Represents a relationship between two entities
- **QueueItem**: Represents an item in the crawler queue

### Core Components

- **Queue Manager**: Handles queue operations with persistence
- **Crawler**: Manages the crawling process with parallel processing
- **Data Processors**: Process different types of data and generate entities and relationships

### API Endpoints

#### Queue Management
- **POST /api/queue**: Add a new item to the queue
- **DELETE /api/queue/:id**: Remove an item from the queue
- **GET /api/queue/pending**: Get all pending items in the queue
- **GET /api/queue/processing**: Get all items currently being processed

#### Crawler Control
- **POST /api/crawler/start**: Start the crawler
- **POST /api/crawler/stop**: Stop the crawler

#### Data Retrieval
- **GET /api/entities**: Get entities with optional filters
- **GET /api/relationships**: Get entity relationships with optional filters

#### Graph Visualization
- **GET /api/graph**: Get graph data with optional filters and depth
- **GET /api/graph/entity/:id**: Get graph data for a specific entity
- **GET /api/graph/type/:type**: Get graph data for entities of a specific type
- **GET /api/graph/roots**: Get graph data for root entities (entities with no parent)

## Setup

### Prerequisites

- Go 1.16 or higher
- PostgreSQL 12 or higher
- SQLite (for GeoLite2 database)
- CGO enabled for SQLite support

### Environment Variables

- `DB_HOST`: PostgreSQL host (default: localhost)
- `DB_PORT`: PostgreSQL port (default: 5432)
- `DB_USER`: PostgreSQL user (default: postgres)
- `DB_PASSWORD`: PostgreSQL password (default: postgres)
- `DB_NAME`: PostgreSQL database name (default: crawler)
- `DB_SSLMODE`: PostgreSQL SSL mode (default: disable)
- `PORT`: Server port (default: 8080)
- `WORKER_POOL`: Number of parallel workers (default: 5)
- `AUTO_START_CRAWLER`: Automatically start the crawler on application startup (default: true)

### Running the Application

1. Set up PostgreSQL database
2. Configure environment variables
3. Run the application:

```bash
go run main.go
```

## Usage Examples

### Adding an Item to the Queue

```bash
curl -X POST http://localhost:8080/api/queue \
  -H "Content-Type: application/json" \
  -d '{
    "data_type": "sample",
    "data": {
      "name": "Example Entity",
      "children": [
        {"name": "Child Entity 1"},
        {"name": "Child Entity 2"}
      ]
    }
  }'
```

### Starting the Crawler

By default, the crawler starts automatically when the application launches. If you've disabled auto-start, you can start it manually:

```bash
curl -X POST http://localhost:8080/api/crawler/start
```

### Getting Entities

```bash
curl http://localhost:8080/api/entities?asset_type=sample
```

## Extending the System

To add support for new data types, implement the `DataProcessor` interface and register it with the crawler:

```go
type CustomProcessor struct{}

func (p *CustomProcessor) Process(item *models.QueueItem) ([]*models.QueueItem, *models.Entity, []*models.EntityRelationship, error) {
    // Implementation

    // Important: Check if entity already exists in the database
    // before creating a new one to avoid duplicates
    var existingEntity models.Entity
    result := db.DB.Where("asset_type = ? AND asset_name = ?", dataType, name).First(&existingEntity)
    if result.Error == nil {
        // Entity already exists, use it instead of creating a new one
        // ...
    }
}

// Register in main.go
crawlerInstance.RegisterProcessor("custom", &CustomProcessor{})
```

## Duplicate Prevention

The system prevents duplicate entity processing through multiple mechanisms:

1. **In-memory cache**: The crawler maintains an in-memory cache of processed entity IDs
2. **Database checks**: Before processing an entity, the system checks if it already exists in the database
3. **Queue filtering**: New queue items are checked against existing entities to avoid re-crawling
4. **Relationship deduplication**: Relationships are only created if they don't already exist

## Entity Deletion

The system provides functionality to delete entities and their relationships while preserving important connections:

### Delete Entity with Children

The `delete_entity_with_children` function deletes an entity and all its children, with the following preservation rules:

1. **Preserves the direct parent**: The direct parent of the root entity being deleted is preserved
2. **Preserves external relationships**: Children that have relationships with entities outside the deletion tree are preserved
3. **Preserves entities with external relationships**: Any entity in the tree that has a relationship with an entity outside the tree is preserved

#### Usage

```bash
curl -X DELETE http://localhost:8080/api/entities/:id/with-children
```

#### Deletion Rules

- The entity specified by `:id` will be deleted
- All children of the entity will be deleted, unless they have relationships with entities outside the deletion tree
- The direct parent of the entity will be preserved, but the relationship between the parent and the entity will be deleted
- Any relationships between preserved entities and deleted entities will be removed
- Foreign key constraints are properly handled to avoid constraint violations

#### Implementation Details

The deletion process follows these steps to ensure data integrity:

1. Identify all entities in the tree that need to be preserved due to external relationships
2. Update parent references for preserved direct children
3. Delete relationships for entities that will be deleted (from bottom up)
4. Delete the entities themselves (from bottom up)
5. Handle the root entity's relationships separately, preserving the parent relationship until the end
6. Delete the root entity

## GeoLite2 Database

The system uses the GeoLite2 ASN database for IP address and ASN lookups. This database is provided by MaxMind and requires a license key to download.

### Setup

1. Sign up for a MaxMind account at [https://www.maxmind.com/en/geolite2/signup](https://www.maxmind.com/en/geolite2/signup)
2. Generate a license key in your MaxMind account dashboard
3. Download the GeoLite2 ASN database in SQLite format
4. Place the downloaded `GeoLite2-ASN.db` file in the `data` directory

### Mock Database for Development

For development or testing, you can create a mock database using the provided scripts:

```bash
# On Linux/macOS
chmod +x data/create-mock-db.sh
./data/create-mock-db.sh

# On Windows
data\create-mock-db.bat
```

### Docker Configuration

The Docker containers are configured to use CGO_ENABLED=1 to support SQLite. The database files in the `data` directory will be copied into the Docker containers during the build process.

### Troubleshooting

#### CGO_ENABLED=0 Error

If you encounter the error:

```
invalid asndb: failed to ping sqlite database 'GeoLite2-ASN.db?_journal_mode=WAL&_busy_timeout=5000': Binary was compiled with 'CGO_ENABLED=0', go-sqlite3 requires cgo to work. This is a stub
```

It means that the application was compiled without CGO support. Make sure to:

1. Build with `CGO_ENABLED=1` when compiling manually:
   ```bash
   CGO_ENABLED=1 go build -o crawler .
   ```

2. Use the provided Dockerfiles which have been configured with CGO support

3. Ensure the GeoLite2-ASN.db file is in the correct location (in the `data` directory)

#### pread64/pwrite64 Compilation Errors

If you encounter compilation errors related to `pread64`, `pwrite64`, or `off64_t`:

```
sqlite3-binding.c:35901:42: error: 'pread64' undeclared here (not in a function); did you mean 'pread'?
```

This is a known issue with the go-sqlite3 package on some systems. The project includes scripts to fix this issue:

1. If using Docker, the fix is automatically applied during the build process

2. If building locally, apply the fix manually:
   ```bash
   # On Linux/macOS
   chmod +x fix-sqlite3.sh
   ./fix-sqlite3.sh

   # On Windows
   fix-sqlite3.bat
   ```

The fix replaces the 64-bit file offset functions (`pread64`, `pwrite64`) with their standard counterparts (`pread`, `pwrite`) and replaces `off64_t` with `off_t`.

## Graph Visualization

The system includes a web-based visualization interface for exploring entity relationships:

### Features

- Interactive force-directed graph visualization
- Filter entities by type, name, and relationship depth
- View detailed entity information on click
- Color-coded nodes and links for better visual understanding
- Support for hierarchical and cross-reference relationships

### Usage

1. Start the crawler system
2. Open a web browser and navigate to `http://localhost:8080`
3. Use the sidebar filters to explore different entity relationships
4. Click on nodes to view detailed entity information

### Graph Data Structure

The graph visualization uses the following data structure:

```go
// GraphNode represents a node in the visualization
type GraphNode struct {
	ID         string      `json:"id"`         // Entity ID
	Name       string      `json:"name"`       // Entity name
	Type       string      `json:"type"`       // Entity type
	Attributes interface{} `json:"attributes"` // Entity attributes
}

// GraphLink represents a link between nodes
type GraphLink struct {
	Source   string `json:"source"`           // Source entity ID
	Target   string `json:"target"`           // Target entity ID
	Label    string `json:"label,omitempty"`  // Relationship label
	Color    string `json:"color,omitempty"`  // Link color
	LinkType string `json:"linkType,omitempty"` // Link type
}

// GraphData contains all nodes and links
type GraphData struct {
	Nodes []GraphNode `json:"nodes"`
	Links []GraphLink `json:"links"`
}
```
