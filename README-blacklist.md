# Blacklist Manager Feature

This feature allows you to prevent the crawler from processing specific entities or entire entity types. It's useful for:

1. Blocking known malicious or irrelevant domains
2. Preventing crawling of sensitive or private information
3. Limiting the scope of your crawl to specific entity types
4. Improving crawler performance by skipping unwanted entities

## How It Works

The blacklist manager maintains a database of entities that should not be crawled. When the crawler processes queue items, it checks if the entity is blacklisted before proceeding.

### Blacklist Checks

The system checks the blacklist at three key points:

1. **When adding items to the queue**: Prevents blacklisted entities from being added to the queue
2. **Before processing queue items**: Skips processing of blacklisted entities that are already in the queue
3. **When generating new queue items**: Prevents the crawler from adding new items that match blacklisted entities

### Blacklist Matching

Entities can be blacklisted in several ways:

1. **By type**: Block all entities of a specific type (e.g., all domains)
2. **By name**: Block specific entities by their name (e.g., a specific domain)
3. **By type and name**: Block a specific entity of a specific type

## Using the Blacklist Manager

### Adding Entities to the Blacklist

1. Navigate to the Blacklist page
2. Click the "Add to Blacklist" button
3. Select the asset type from the dropdown
4. Optionally enter an asset name (leave empty to block the entire type)
5. Add a description to document why this entity is blacklisted
6. Click "Add"

### Removing Entities from the Blacklist

1. Navigate to the Blacklist page
2. Find the entity you want to remove
3. Click the delete icon
4. Confirm the removal

### Filtering the Blacklist

You can filter the blacklist by:

1. Asset type: Show only entities of a specific type
2. Asset name: Search for specific entity names

## Implementation Details

The blacklist feature consists of:

1. **Database Model**: `BlacklistedEntity` table to store blacklisted entities
2. **Cache System**: In-memory cache for fast blacklist checking
3. **API Endpoints**: REST endpoints for managing the blacklist
4. **UI Components**: Frontend interface for blacklist management

### Performance Considerations

- The blacklist is cached in memory for fast lookups
- The cache is refreshed when entities are added or removed from the blacklist
- The crawler checks the cache before making database queries

## Best Practices

1. **Be specific**: When possible, blacklist specific entities rather than entire types
2. **Document reasons**: Always add a description explaining why an entity is blacklisted
3. **Regular review**: Periodically review the blacklist to ensure it's still relevant
4. **Test after changes**: After making significant changes to the blacklist, test the crawler to ensure it behaves as expected
