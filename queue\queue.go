package queue

import (
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/crawler/db"
	"github.com/crawler/models"
	"gorm.io/gorm"
)

// Crawler status constants
const (
	StatusStopped = "stopped"
	StatusRunning = "running"
	StatusWaiting = "waiting"
	StatusError   = "error"
)

// QueueManager handles the queue operations
type QueueManager struct {
	mu            sync.Mutex
	processing    bool
	status        string
	statusMessage string
	workerCount   int
	workerPool    chan struct{}
	stopChan      chan struct{}
	processingMap map[string]bool
	// Counter for logging when waiting for new items
	waitCounter int
}

// NewQueueManager creates a new queue manager
func NewQueueManager(workerCount int) *QueueManager {
	qm := &QueueManager{
		processing:    false,
		status:        StatusStopped,
		statusMessage: "Crawler is stopped",
		workerCount:   workerCount,
		workerPool:    make(chan struct{}, workerCount),
		stopChan:      make(chan struct{}),
		processingMap: make(map[string]bool),
		waitCounter:   0,
	}

	// Recover any items that were left in the processing state
	qm.recoverProcessingItems()

	return qm
}

// AddItem adds a new item to the queue
func (qm *QueueManager) AddItem(item *models.QueueItem) error {
	item.Status = "pending"
	err := db.DB.Create(item).Error
	if err == nil {
		// Reset wait counter when new items are added
		qm.mu.Lock()
		qm.waitCounter = 0
		// If the crawler was waiting, update status to running
		if qm.status == StatusWaiting && qm.processing {
			qm.status = StatusRunning
			qm.statusMessage = "Crawler is processing items"
		}
		qm.mu.Unlock()
		fmt.Printf("Added new queue item: %s (Type: %s)\n", item.ID, item.DataType)
	}
	return err
}

// RemoveItem removes an item from the queue
func (qm *QueueManager) RemoveItem(id string) error {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	// Check if the item is currently being processed
	if qm.processingMap[id] {
		return errors.New("cannot remove item that is currently being processed")
	}

	return db.DB.Delete(&models.QueueItem{}, "id = ?", id).Error
}

// GetPendingItems returns all pending items in the queue
func (qm *QueueManager) GetPendingItems() ([]models.QueueItem, error) {
	var items []models.QueueItem
	err := db.DB.Where("status = ?", "pending").Find(&items).Error
	return items, err
}

// GetProcessingItems returns all items currently being processed
func (qm *QueueManager) GetProcessingItems() ([]models.QueueItem, error) {
	var items []models.QueueItem
	err := db.DB.Where("status = ?", "processing").Find(&items).Error
	return items, err
}

// GetPendingItemsPaginated returns paginated pending items in the queue
func (qm *QueueManager) GetPendingItemsPaginated(page, limit int) ([]models.QueueItem, int64, error) {
	var items []models.QueueItem
	var totalCount int64

	// Get total count
	if err := db.DB.Model(&models.QueueItem{}).Where("status = ?", "pending").Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get paginated items
	err := db.DB.Where("status = ?", "pending").Order("created_at DESC").Limit(limit).Offset(offset).Find(&items).Error
	if err != nil {
		return nil, 0, err
	}

	return items, totalCount, nil
}

// GetProcessingItemsPaginated returns paginated items currently being processed
func (qm *QueueManager) GetProcessingItemsPaginated(page, limit int) ([]models.QueueItem, int64, error) {
	var items []models.QueueItem
	var totalCount int64

	// Get total count
	if err := db.DB.Model(&models.QueueItem{}).Where("status = ?", "processing").Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get paginated items
	err := db.DB.Where("status = ?", "processing").Order("created_at DESC").Limit(limit).Offset(offset).Find(&items).Error
	if err != nil {
		return nil, 0, err
	}

	return items, totalCount, nil
}

// StartProcessing starts the queue processing
func (qm *QueueManager) StartProcessing(processor func(*models.QueueItem) error) error {
	qm.mu.Lock()
	if qm.processing {
		qm.mu.Unlock()
		return errors.New("processing already started")
	}
	qm.processing = true
	qm.status = StatusRunning
	qm.statusMessage = "Crawler is running"
	qm.stopChan = make(chan struct{})
	qm.waitCounter = 0 // Reset wait counter
	qm.mu.Unlock()

	// Recover any items that were left in the processing state
	// This ensures we don't miss any items if the process was terminated unexpectedly
	qm.recoverProcessingItems()

	fmt.Println("Crawler started and waiting for queue items...")
	go qm.processQueue(processor)
	return nil
}

// StopProcessing stops the queue processing
func (qm *QueueManager) StopProcessing() error {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	if !qm.processing {
		return errors.New("processing not started")
	}

	fmt.Println("Stopping crawler...")
	close(qm.stopChan)
	qm.processing = false
	qm.status = StatusStopped
	qm.statusMessage = "Crawler is stopped"
	return nil
}

// GetStatus returns the current status of the crawler
func (qm *QueueManager) GetStatus() (string, string) {
	qm.mu.Lock()
	defer qm.mu.Unlock()
	return qm.status, qm.statusMessage
}

// processQueue continuously processes items from the queue
func (qm *QueueManager) processQueue(processor func(*models.QueueItem) error) {
	for {
		select {
		case <-qm.stopChan:
			return
		default:
			// Get next pending item
			var item models.QueueItem
			err := db.DB.Transaction(func(tx *gorm.DB) error {
				// Find a pending item
				if err := tx.Where("status = ?", "pending").First(&item).Error; err != nil {
					if errors.Is(err, gorm.ErrRecordNotFound) {
						// No pending items, wait for new items
						return gorm.ErrRecordNotFound
					}
					return err
				}

				// Mark as processing
				item.Status = "processing"
				item.LastAttempt = time.Now()
				item.Attempts++
				return tx.Save(&item).Error
			})

			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					// No pending items, wait for new items
					// Log that we're waiting for new items, but not too frequently
					qm.mu.Lock()
					qm.waitCounter++
					// Update status to waiting
					qm.status = StatusWaiting
					qm.statusMessage = "Crawler is waiting for new queue items"
					if qm.waitCounter%12 == 0 { // Log approximately every minute (12 * 5 seconds)
						fmt.Println("Crawler is waiting for new queue items...")
					}
					qm.mu.Unlock()

					time.Sleep(5 * time.Second) // Wait longer between checks to reduce database load
					continue
				}
				// Log error and continue
				fmt.Printf("Error fetching queue items: %v\n", err)
				qm.mu.Lock()
				qm.status = StatusError
				qm.statusMessage = fmt.Sprintf("Error: %v", err)
				qm.mu.Unlock()
				time.Sleep(1 * time.Second) // Brief pause on error
				continue
			}

			// Process the item in a worker
			qm.workerPool <- struct{}{} // Acquire a worker
			qm.mu.Lock()
			qm.processingMap[item.ID] = true
			// Update status to running
			qm.status = StatusRunning
			qm.statusMessage = "Crawler is processing items"
			qm.mu.Unlock()

			go func(item models.QueueItem) {
				defer func() {
					<-qm.workerPool // Release the worker
					qm.mu.Lock()
					delete(qm.processingMap, item.ID)
					qm.mu.Unlock()
				}()

				// Process the item
				err := processor(&item)

				// Update item status based on processing result
				status := "completed"
				errorMsg := ""
				if err != nil {
					status = "failed"
					errorMsg = err.Error()
				}

				db.DB.Model(&item).Updates(map[string]any{
					"status":        status,
					"error_message": errorMsg,
				})
			}(item)
		}
	}
}
