package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// StatsHandler handles statistics-related API endpoints
type StatsHandler struct {
	statsService *StatsService
}

// NewStatsHandler creates a new stats handler
func NewStatsHandler() *StatsHandler {
	return &StatsHandler{
		statsService: NewStatsService(),
	}
}

// GetEntityStats returns statistics about entities
func (h *StatsHandler) GetEntityStats(c *gin.Context) {
	stats, err := h.statsService.GetEntityStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}
