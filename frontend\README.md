# Crawler System Frontend

A React-based frontend for the Crawler System, built with Material-UI (MUI).

## Features

- Dashboard for system overview and control
- Entity management and visualization
- Queue management interface
- Interactive graph visualization of entity relationships
- Settings configuration

## Technologies Used

- React with TypeScript
- Material-UI (MUI) for UI components
- React Router for navigation
- Axios for API communication
- React Force Graph for visualization
- Notistack for notifications

## Getting Started

### Prerequisites

- Node.js 16+ and npm

### Installation

1. Clone the repository
2. Navigate to the frontend directory:
   ```
   cd frontend
   ```
3. Install dependencies:
   ```
   npm install
   ```

### Development

Run the development server:
```
npm run dev
```

The application will be available at http://localhost:3000.

### Building for Production

Build the application:
```
npm run build
```

The built files will be in the `dist` directory.

## Project Structure

- `src/components/` - Reusable UI components
  - `Layout/` - Layout components (AppBar, Drawer, etc.)
  - `Entity/` - Entity-related components
  - `Queue/` - Queue-related components
  - `Graph/` - Graph visualization components
- `src/pages/` - Page components
- `src/services/` - API services
- `src/theme.ts` - MUI theme configuration

## API Integration

The frontend communicates with the backend API through the `apiService` defined in `src/services/api.ts`. The service provides methods for all API endpoints:

- Queue management
- Crawler control
- Entity and relationship retrieval
- Graph data for visualization

## Features

### Dashboard

- System overview with key statistics
- Crawler control (start/stop)
- Quick access to main features

### Entities Page

- List and search entities
- View entity details
- Navigate to entity graph visualization

### Queue Management

- View pending and processing queue items
- Add new items to the queue
- Remove items from the queue

### Graph Visualization

- Interactive force-directed graph
- Filter by entity type, name, and relationship depth
- View entity details
- Navigate between related entities
- Save the graph as a PNG image
- Group entities by type
- Requeue entities for crawling
- Delete entities and their children

### Settings

- Configure system settings
- UI preferences

## Saving the Graph as an Image

The Entity Relationship Graph can be saved as a PNG image in two ways:

1. **From the main graph view**: Click the "Save as Image" button in the top-right corner of the graph controls.
2. **From the node details dialog**: When viewing details of a selected node, click the "Save as Image" button in the dialog actions.

The saved image will include the current state of the graph, including all visible nodes and relationships, with the current layout and styling. The image is saved with a filename that includes the current date and, if a node is selected, the node ID.
