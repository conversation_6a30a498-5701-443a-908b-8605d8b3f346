import axios from 'axios';

// Create axios instance with base URL
const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types
export interface Entity {
  id: string;
  asset_type: string;
  asset_name: string;
  parent_id?: string;
  attributes: any;
  do_follow: boolean;
  created_at: string;
  updated_at: string;
}

export interface EntityRelationship {
  relationship_id: number;
  from_entity_id: string;
  to_entity_id: string;
  label: string;
  style?: string;
  color?: string;
  created_at: string;
}

export interface QueueItem {
  id: string;
  parent_id?: string;
  data_type: string;
  data: any;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  updated_at: string;
  attempts: number;
  last_attempt?: string;
  error_message?: string;
  max_depth?: number;
  current_depth?: number;
}

export interface BlacklistedEntity {
  id: string;
  asset_type: string;
  asset_name: string;
  pattern: string;
  description: string;
  created_at: string;
  updated_at: string;
}

export interface GraphNode {
  id: string;
  name: string;
  type: string;
  attributes: any;
}

export interface GraphLink {
  source: string;
  target: string;
  label?: string;
  color?: string;
  linkType?: string;
}

export interface GraphData {
  nodes: GraphNode[];
  links: GraphLink[];
}

export interface EntityStats {
  totalCount: number;
  typeDistribution: Record<string, number>;
  createdLast24Hours: number;
  createdLastWeek: number;
  createdLastMonth: number;
}

// API functions
export const apiService = {
  // Queue Management
  getQueuePending: (params?: { page?: number; limit?: number }) =>
    api.get<{ data: QueueItem[]; total_count: number; total_pages: number; current_page: number; limit: number }>('/queue/pending', { params }),
  getQueueProcessing: (params?: { page?: number; limit?: number }) =>
    api.get<{ data: QueueItem[]; total_count: number; total_pages: number; current_page: number; limit: number }>('/queue/processing', { params }),
  getQueueProcessed: (params?: { page?: number; limit?: number }) =>
    api.get<{ data: QueueItem[]; total_count: number; total_pages: number; current_page: number; limit: number }>('/queue/processed', { params }),
  addQueueItem: (item: Omit<QueueItem, 'id' | 'created_at' | 'updated_at' | 'attempts' | 'last_attempt' | 'error_message' | 'status'>) =>
    api.post<{ id: string }>('/queue', item),
  removeQueueItem: (id: string) => api.delete(`/queue/${id}`),

  // Crawler Control
  startProcessing: () => api.post('/crawler/start'),
  stopProcessing: () => api.post('/crawler/stop'),
  getCrawlerStatus: () => api.get<{ status: string; message: string }>('/crawler/status'),
  getManualCrawlTypes: () => api.get<{ manual_crawl_types: string[] }>('/crawler/manual-types'),

  // Entity and Relationship Retrieval
  getEntities: (params?: { id?: string; asset_type?: string; asset_name?: string; parent_id?: string; page?: number; limit?: number }) =>
    api.get<{ data: Entity[]; total_count: number; total_pages: number; current_page: number; limit: number }>('/entities', { params }),
  requeueEntity: (id: string, depth?: number) => api.post<{ message: string, depth: number }>(`/entities/${id}/requeue${depth !== undefined ? `?depth=${depth}` : ''}`),
  updateEntityDoFollow: (id: string, doFollow: boolean) => api.patch<{ message: string, entity: Entity }>(`/entities/${id}/do-follow`, { do_follow: doFollow }),
  deleteEntityWithChildren: (id: string) => api.delete<{
    message: string;
    deleted_count: number;
    preserved_count: number;
    entity_type: string;
    entity_name: string;
    total_descendants: number;
    external_relations: number;
  }>(`/entities/${id}`),
  getRelationships: (params?: { from_entity_id?: string; to_entity_id?: string; label?: string }) =>
    api.get<EntityRelationship[]>('/relationships', { params }),

  // Graph Data
  getGraph: (params?: { asset_type?: string; asset_name?: string; parent_id?: string; depth?: number }) =>
    api.get<GraphData>('/graph', { params }),
  getEntityGraph: (id: string) => api.get<GraphData>(`/graph/entity/${id}`),
  getTypeGraph: (type: string) => api.get<GraphData>(`/graph/type/${type}`),
  getRootGraph: () => api.get<GraphData>('/graph/roots'),

  // Blacklist Management
  getBlacklist: (params?: { asset_type?: string; asset_name?: string }) =>
    api.get<BlacklistedEntity[]>('/blacklist', { params }),
  addBlacklistedEntity: (entity: Omit<BlacklistedEntity, 'id' | 'created_at' | 'updated_at'>) =>
    api.post<BlacklistedEntity>('/blacklist', entity),
  removeBlacklistedEntity: (id: string) => api.delete(`/blacklist/${id}`),
  testBlacklistPattern: (data: { asset_type: string; asset_name: string; pattern: string }) =>
    api.post<{ matches: boolean; sql_matches: boolean; asset_type: string; asset_name: string; pattern: string }>('/blacklist/test-pattern', data),

  // Statistics
  getEntityStats: () => api.get<EntityStats>('/stats/entities'),
};

export default apiService;
