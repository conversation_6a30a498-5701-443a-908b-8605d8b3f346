import React from 'react';
import {
  AppBar as <PERSON>i<PERSON>ppBar,
  <PERSON><PERSON><PERSON>,
  Typography,
  Button,
  IconButton,
  Box,
  useTheme,
  useMediaQuery
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import { Link as RouterLink } from 'react-router-dom';

interface AppBarProps {
  title: string;
  onDrawerToggle: () => void;
}

const AppBar: React.FC<AppBarProps> = ({ title, onDrawerToggle }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <MuiAppBar position="fixed" color="primary" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
      <Toolbar>
        <IconButton
          color="inherit"
          aria-label="open drawer"
          edge="start"
          onClick={onDrawerToggle}
          sx={{ mr: 2, display: { md: 'none' } }}
        >
          <MenuIcon />
        </IconButton>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          {title}
        </Typography>

        {!isMobile && (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button color="inherit" component={RouterLink} to="/">
              Dashboard
            </Button>
            <Button color="inherit" component={RouterLink} to="/entities">
              Entities
            </Button>
            <Button color="inherit" component={RouterLink} to="/queue">
              Queue
            </Button>
            <Button color="inherit" component={RouterLink} to="/graph">
              Graph
            </Button>
            <Button color="inherit" component={RouterLink} to="/blacklist">
              Blacklist
            </Button>
            <Button color="inherit" component={RouterLink} to="/stats">
              Statistics
            </Button>
          </Box>
        )}
      </Toolbar>
    </MuiAppBar>
  );
};

export default AppBar;
