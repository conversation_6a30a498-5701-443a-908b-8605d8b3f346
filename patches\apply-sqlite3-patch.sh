#!/bin/bash

# This script applies a patch to the go-sqlite3 package to fix compilation issues
# with 64-bit file offset functions on some systems.

set -e

# Get the go-sqlite3 package directory
SQLITE3_DIR=$(go list -f '{{.Dir}}' github.com/mattn/go-sqlite3)

if [ -z "$SQLITE3_DIR" ]; then
    echo "Error: go-sqlite3 package not found. Please run 'go get github.com/mattn/go-sqlite3' first."
    exit 1
fi

echo "Found go-sqlite3 package at: $SQLITE3_DIR"

# Apply the patch
echo "Applying patch to fix 64-bit file offset functions..."
patch -p1 -d "$SQLITE3_DIR" < "$(dirname "$0")/sqlite3-fix.patch"

echo "Patch applied successfully!"
echo "Now you can build your application with CGO_ENABLED=1"
