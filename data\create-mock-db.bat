@echo off
echo Creating mock GeoLite2-ASN.db file...

REM Check if sqlite3 is installed
where sqlite3 >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: sqlite3 is not installed. Please install it first.
    exit /b 1
)

REM Create a temporary SQL file
echo CREATE TABLE asn_blocks ( > create-db.sql
echo   network TEXT, >> create-db.sql
echo   autonomous_system_number INTEGER, >> create-db.sql
echo   autonomous_system_organization TEXT >> create-db.sql
echo ); >> create-db.sql
echo. >> create-db.sql
echo -- Add some sample data >> create-db.sql
echo INSERT INTO asn_blocks VALUES ('192.168.0.0/16', 64512, 'Example AS'); >> create-db.sql
echo INSERT INTO asn_blocks VALUES ('10.0.0.0/8', 64513, 'Private Network AS'); >> create-db.sql
echo INSERT INTO asn_blocks VALUES ('172.16.0.0/12', 64514, 'Internal AS'); >> create-db.sql
echo INSERT INTO asn_blocks VALUES ('8.8.8.0/24', 15169, 'Google LLC'); >> create-db.sql
echo INSERT INTO asn_blocks VALUES ('1.1.1.0/24', 13335, 'Cloudflare, Inc.'); >> create-db.sql

REM Create the database
sqlite3 GeoLite2-ASN.db < create-db.sql

REM Clean up
del create-db.sql

echo Mock GeoLite2-ASN.db created successfully.
