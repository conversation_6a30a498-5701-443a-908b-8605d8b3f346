package crawler

import (
	"encoding/json"
	"fmt"

	"github.com/crawler/db"
	"github.com/crawler/models"
	"github.com/google/uuid"
)

// requeueAllChildren recursively requeues all children of an entity
func (c *Crawler) requeueAllChildren(parentID string, maxDepth int) {
	// Check if the parent entity has DoFollow set to false
	var parentEntity models.Entity
	if err := db.DB.First(&parentEntity, "id = ?", parentID).Error; err != nil {
		fmt.Printf("Error finding parent entity %s: %v\n", parentID, err)
		return
	}

	// If parent has DoFollow=false, don't requeue its children
	if !parentEntity.DoFollow {
		fmt.Printf("Skipping requeuing children because parent entity %s has DoFollow=false\n", parentID)
		return
	}

	// Find all direct children of the entity
	var children []models.Entity
	if err := db.DB.Where("parent_id = ?", parentID).Find(&children).Error; err != nil {
		fmt.Printf("Error finding children of entity %s: %v\n", parentID, err)
		return
	}

	// Requeue each child
	for _, child := range children {
		// Skip manual-only entity types during requeue operations
		// These types need to be manually requeued by the user
		if c.manualCrawlTypes[child.AssetType] {
			fmt.Printf("Skipping requeue of %s (type: %s) - manual crawl only\n", child.ID, child.AssetType)
			continue
		}

		// Create a queue item for the child
		item := &models.QueueItem{
			ID:           uuid.New().String(),
			DataType:     child.AssetType,
			Status:       "pending",
			MaxDepth:     maxDepth,
			CurrentDepth: 0,
			ParentID:     &parentID,
			IsRequeue:    true, // Mark as a requeue operation
		}

		// Extract entity attributes to use as data
		var attributes map[string]interface{}
		if err := json.Unmarshal(child.Attributes, &attributes); err != nil {
			fmt.Printf("Failed to unmarshal attributes for entity %s: %v\n", child.ID, err)
			continue
		}

		// Add entity ID and name to the data
		data := map[string]interface{}{
			"id":   child.ID,
			"name": child.AssetName,
		}

		// Merge attributes into data
		for k, v := range attributes {
			data[k] = v
		}

		// Set the data in the queue item
		if err := item.SetData(data); err != nil {
			fmt.Printf("Failed to set data for entity %s: %v\n", child.ID, err)
			continue
		}

		// Add to queue without checking if it already exists
		if err := c.queueManager.AddItem(item); err != nil {
			fmt.Printf("Failed to add entity %s to queue: %v\n", child.ID, err)
		}

		// Recursively requeue this child's children
		c.requeueAllChildren(child.ID, maxDepth)
	}
}
