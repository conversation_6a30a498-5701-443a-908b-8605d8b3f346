import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  CircularProgress,
  Card,
  CardContent,
  Divider,
  Button,
  Chip,
  Stack,
  useTheme
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import BarChartIcon from '@mui/icons-material/BarChart';
import TimelineIcon from '@mui/icons-material/Timeline';
import PieChartIcon from '@mui/icons-material/PieChart';
import Layout from '../components/Layout/Layout';
import apiService, { EntityStats } from '../services/api';
import { useSnackbar } from 'notistack';

const StatsPage: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<EntityStats | null>(null);

  useEffect(() => {
    fetchStats();

    // Set up periodic refresh of stats
    const intervalId = setInterval(() => {
      fetchStats();
    }, 60000); // Refresh every minute

    return () => clearInterval(intervalId); // Clean up on unmount
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await apiService.getEntityStats();
      setStats(response.data);
    } catch (error) {
      console.error('Error fetching entity statistics:', error);
      enqueueSnackbar('Failed to fetch entity statistics', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Function to get a color for each entity type
  const getTypeColor = (type: string, index: number) => {
    const colors = [
      theme.palette.primary.main,
      theme.palette.secondary.main,
      theme.palette.success.main,
      theme.palette.warning.main,
      theme.palette.error.main,
      theme.palette.info.main,
    ];
    return colors[index % colors.length];
  };

  return (
    <Layout title="Statistics">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" gutterBottom>
            Entity Statistics
          </Typography>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchStats}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>

        {loading && !stats ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : !stats ? (
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1" color="text.secondary">
              No statistics available. Try refreshing the page.
            </Typography>
          </Paper>
        ) : (
          <>
            {/* Summary Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Total Entities
                    </Typography>
                    <Typography variant="h3" align="center" sx={{ my: 2 }}>
                      {stats.totalCount}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Last 24 Hours
                    </Typography>
                    <Typography variant="h3" align="center" sx={{ my: 2 }}>
                      {stats.createdLast24Hours}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Last Week
                    </Typography>
                    <Typography variant="h3" align="center" sx={{ my: 2 }}>
                      {stats.createdLastWeek}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Last Month
                    </Typography>
                    <Typography variant="h3" align="center" sx={{ my: 2 }}>
                      {stats.createdLastMonth}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* Type Distribution */}
            <Paper sx={{ p: 3, mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PieChartIcon sx={{ mr: 1 }} color="primary" />
                <Typography variant="h6">Entity Type Distribution</Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />
              
              <Grid container spacing={2}>
                {Object.entries(stats.typeDistribution).map(([type, count], index) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={type}>
                    <Box sx={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      p: 2,
                      border: `1px solid ${theme.palette.divider}`,
                      borderRadius: 1,
                      '&:hover': {
                        backgroundColor: theme.palette.action.hover
                      }
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box 
                          sx={{ 
                            width: 12, 
                            height: 12, 
                            borderRadius: '50%', 
                            bgcolor: getTypeColor(type, index),
                            mr: 1 
                          }} 
                        />
                        <Typography variant="body1">{type}</Typography>
                      </Box>
                      <Chip 
                        label={count} 
                        size="small" 
                        sx={{ 
                          bgcolor: getTypeColor(type, index),
                          color: theme.palette.getContrastText(getTypeColor(type, index))
                        }} 
                      />
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Paper>

            {/* Additional Statistics Placeholder */}
            <Paper sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TimelineIcon sx={{ mr: 1 }} color="primary" />
                <Typography variant="h6">Growth Trends</Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />
              
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body1" color="text.secondary">
                  Detailed growth trends will be available in a future update.
                </Typography>
              </Box>
            </Paper>
          </>
        )}
      </Box>
    </Layout>
  );
};

export default StatsPage;
