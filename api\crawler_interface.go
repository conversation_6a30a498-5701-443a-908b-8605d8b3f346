package api

import "github.com/crawler/models"

// CrawlerInterface defines the methods needed by the API handlers
type CrawlerInterface interface {
	Start() error
	Stop() error
	GetStatus() (string, string)
	AddToQueue(item *models.QueueItem) error
	RemoveFromQueue(id string) error
	GetPendingItems() ([]models.QueueItem, error)
	GetProcessingItems() ([]models.QueueItem, error)
	GetProcessedItems() ([]models.QueueItem, error)
	GetPendingItemsPaginated(page, limit int) ([]models.QueueItem, int64, error)
	GetProcessingItemsPaginated(page, limit int) ([]models.QueueItem, int64, error)
	GetProcessedItemsPaginated(page, limit int) ([]models.QueueItem, int64, error)
	RefreshBlacklistCache()
	RequeueEntity(entityID string) error
	RequeueEntityWithDepth(entityID string, maxDepth int) error
	IsManualCrawlOnly(dataType string) bool
}
