package api

import (
	"github.com/crawler/crawler"
	"github.com/gin-gonic/gin"
)

// SetupRouter sets up the API routes
func SetupRouter(crawler *crawler.Crawler) *gin.Engine {
	router := gin.Default()

	// Create handlers
	handler := NewHandler(crawler)
	graphHandler := NewGraphHandler()
	blacklistHandler := NewBlacklistHandler(crawler)
	statsHandler := NewStatsHandler()

	// API routes
	api := router.Group("/api")
	{
		// Queue management
		api.POST("/queue", handler.AddQueueItem)
		api.DELETE("/queue/:id", handler.RemoveQueueItem)
		api.GET("/queue/pending", handler.GetPendingItems)
		api.GET("/queue/processing", handler.GetProcessingItems)
		api.GET("/queue/processed", handler.GetProcessedItems)

		// Crawler control
		api.POST("/crawler/start", handler.StartProcessing)
		api.POST("/crawler/stop", handler.StopProcessing)
		api.GET("/crawler/status", handler.GetCrawlerStatus)
		api.GET("/crawler/manual-types", handler.GetManualCrawlTypes)

		// Entity and relationship retrieval
		api.GET("/entities", handler.GetEntities)
		api.GET("/relationships", handler.GetEntityRelationships)
		api.POST("/entities/:id/requeue", handler.RequeueEntity)
		api.PATCH("/entities/:id/do-follow", handler.UpdateEntityDoFollow)
		api.DELETE("/entities/:id", handler.DeleteEntityWithChildren)

		// Graph data for visualization
		api.GET("/graph", graphHandler.GetGraphData)
		api.GET("/graph/entity/:id", graphHandler.GetEntityGraph)
		api.GET("/graph/type/:type", graphHandler.GetEntitiesByTypeGraph)
		api.GET("/graph/roots", graphHandler.GetRootEntitiesGraph)

		// Blacklist management
		api.GET("/blacklist", blacklistHandler.GetBlacklistedEntities)
		api.POST("/blacklist", blacklistHandler.AddBlacklistedEntity)
		api.DELETE("/blacklist/:id", blacklistHandler.RemoveBlacklistedEntity)
		api.POST("/blacklist/test-pattern", blacklistHandler.TestBlacklistPattern)

		// Statistics
		api.GET("/stats/entities", statsHandler.GetEntityStats)
	}

	return router
}
