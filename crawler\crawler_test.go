package crawler

import (
	"testing"

	"github.com/crawler/models"
	"github.com/google/uuid"
)

func TestCreateQueueItem(t *testing.T) {
	// Create a parent entity
	parent := &models.Entity{
		ID:        uuid.New().String(),
		AssetType: "test",
		AssetName: "Test Entity",
	}

	// Test data
	testData := map[string]interface{}{
		"name": "Test Data",
		"url":  "https://example.com",
	}

	// Create queue item
	item, err := CreateQueueItem(parent, "test", testData)
	if err != nil {
		t.Fatalf("Failed to create queue item: %v", err)
	}

	// Verify queue item
	if item.DataType != "test" {
		t.<PERSON>("Expected data type 'test', got '%s'", item.DataType)
	}

	if item.ParentID == nil || *item.ParentID != parent.ID {
		t.Errorf("Expected parent ID '%s', got '%v'", parent.ID, item.ParentID)
	}

	if item.Status != "pending" {
		t.Errorf("Expected status 'pending', got '%s'", item.Status)
	}

	// Verify data
	var data map[string]interface{}
	if err := item.GetData(&data); err != nil {
		t.Fatalf("Failed to get item data: %v", err)
	}

	if data["name"] != "Test Data" {
		t.Errorf("Expected name 'Test Data', got '%v'", data["name"])
	}

	if data["url"] != "https://example.com" {
		t.Errorf("Expected url 'https://example.com', got '%v'", data["url"])
	}
}
