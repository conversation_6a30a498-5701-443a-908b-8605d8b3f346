<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Entity Relationship Visualization</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://unpkg.com/force-graph"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        #container {
            display: flex;
            height: 100vh;
        }
        #sidebar {
            width: 300px;
            padding: 20px;
            background-color: #f5f5f5;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }
        #graph {
            flex-grow: 1;
        }
        h1 {
            font-size: 1.5em;
            margin-bottom: 20px;
        }
        h2 {
            font-size: 1.2em;
            margin-top: 20px;
        }
        button {
            padding: 8px 12px;
            margin: 5px 0;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        select, input {
            width: 100%;
            padding: 8px;
            margin: 5px 0 15px;
            box-sizing: border-box;
        }
        .node-info {
            margin-top: 20px;
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="sidebar">
            <h1>Entity Relationship Explorer</h1>
            
            <h2>Filters</h2>
            <label for="entity-type">Entity Type:</label>
            <select id="entity-type">
                <option value="">All Types</option>
                <!-- Will be populated dynamically -->
            </select>
            
            <label for="search">Search by Name:</label>
            <input type="text" id="search" placeholder="Enter entity name...">
            
            <label for="depth">Relationship Depth:</label>
            <select id="depth">
                <option value="1">1 (Direct connections)</option>
                <option value="2">2 (Connections of connections)</option>
                <option value="3">3 (Extended network)</option>
                <option value="0">All (May be slow)</option>
            </select>
            
            <button id="apply-filters">Apply Filters</button>
            <button id="show-roots">Show Root Entities</button>
            
            <div id="node-info" class="node-info" style="display: none;">
                <h3 id="node-title">Entity Details</h3>
                <div id="node-details"></div>
            </div>
        </div>
        <div id="graph"></div>
    </div>

    <script>
        // Initialize the graph
        const Graph = ForceGraph()
            (document.getElementById('graph'))
            .nodeLabel('name')
            .nodeColor(node => getNodeColor(node.type))
            .linkLabel('label')
            .linkColor(link => link.color || getLinkColor(link.linkType))
            .onNodeClick(node => showNodeInfo(node));

        // Load initial data
        loadGraphData('/api/graph/roots');

        // Populate entity types
        fetchEntityTypes();

        // Event listeners
        document.getElementById('apply-filters').addEventListener('click', applyFilters);
        document.getElementById('show-roots').addEventListener('click', () => loadGraphData('/api/graph/roots'));

        // Functions
        function loadGraphData(url) {
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    Graph.graphData(data);
                })
                .catch(error => console.error('Error loading graph data:', error));
        }

        function fetchEntityTypes() {
            fetch('/api/entities')
                .then(response => response.json())
                .then(entities => {
                    const types = new Set();
                    entities.forEach(entity => types.add(entity.asset_type));
                    
                    const select = document.getElementById('entity-type');
                    types.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type;
                        option.textContent = type;
                        select.appendChild(option);
                    });
                })
                .catch(error => console.error('Error fetching entity types:', error));
        }

        function applyFilters() {
            const entityType = document.getElementById('entity-type').value;
            const search = document.getElementById('search').value;
            const depth = document.getElementById('depth').value;
            
            let url = '/api/graph?depth=' + depth;
            
            if (entityType) {
                url += '&asset_type=' + encodeURIComponent(entityType);
            }
            
            if (search) {
                url += '&asset_name=' + encodeURIComponent(search);
            }
            
            loadGraphData(url);
        }

        function showNodeInfo(node) {
            const nodeInfo = document.getElementById('node-info');
            const nodeTitle = document.getElementById('node-title');
            const nodeDetails = document.getElementById('node-details');
            
            nodeInfo.style.display = 'block';
            nodeTitle.textContent = node.name;
            
            let detailsHtml = `
                <p><strong>ID:</strong> ${node.id}</p>
                <p><strong>Type:</strong> ${node.type}</p>
            `;
            
            if (node.attributes) {
                detailsHtml += '<p><strong>Attributes:</strong></p>';
                try {
                    const attributes = typeof node.attributes === 'string' 
                        ? JSON.parse(node.attributes) 
                        : node.attributes;
                    
                    detailsHtml += '<ul>';
                    for (const [key, value] of Object.entries(attributes)) {
                        detailsHtml += `<li><strong>${key}:</strong> ${JSON.stringify(value)}</li>`;
                    }
                    detailsHtml += '</ul>';
                } catch (e) {
                    detailsHtml += `<p>${node.attributes}</p>`;
                }
            }
            
            nodeDetails.innerHTML = detailsHtml;
            
            // Highlight the node in the graph
            Graph
                .nodeColor(n => n === node ? '#FF5733' : getNodeColor(n.type))
                .linkColor(link => 
                    (link.source === node || link.target === node) 
                        ? '#FF5733' 
                        : (link.color || getLinkColor(link.linkType))
                );
        }

        function getNodeColor(type) {
            // Color mapping for different entity types
            const colorMap = {
                'sample': '#4285F4',  // Blue
                'child': '#34A853',   // Green
                'organization': '#FBBC05',  // Yellow
                'service': '#EA4335',  // Red
                'resource': '#8F00FF',  // Purple
                'user': '#FF6D01',  // Orange
            };
            
            return colorMap[type] || '#999999';  // Default gray
        }

        function getLinkColor(linkType) {
            // Color mapping for different link types
            const colorMap = {
                'hierarchy': '#666666',  // Dark gray
                'cross-reference': '#0099CC',  // Light blue
            };
            
            return colorMap[linkType] || '#CCCCCC';  // Default light gray
        }
    </script>
</body>
</html>
