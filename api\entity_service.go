package api

import (
	"fmt"

	"github.com/crawler/db"
	"github.com/crawler/models"
	"gorm.io/gorm"
)

// EntityService handles operations related to entities
type EntityService struct{}

// NewEntityService creates a new entity service
func NewEntityService() *EntityService {
	return &EntityService{}
}

// DeleteEntityWithChildren deletes an entity and all its children,
// except for children that have relationships with other entities, and preserves the direct parent of the root entity.
// This function properly handles foreign key constraints to avoid constraint violations.
func (s *EntityService) DeleteEntityWithChildren(entityID string) (int, error) {
	// Start a transaction
	tx := db.DB.Begin()
	if tx.Error != nil {
		return 0, fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}

	// Defer rollback in case of error
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get the entity to delete
	var entity models.Entity
	if err := tx.First(&entity, "id = ?", entityID).Error; err != nil {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			return 0, fmt.Errorf("entity not found")
		}
		return 0, fmt.Errorf("failed to find entity: %w", err)
	}

	// Count of deleted entities
	deletedCount := 0

	// Delete the entity and its children recursively
	count, err := s.deleteEntityRecursive(tx, entityID)
	if err != nil {
		tx.Rollback()
		return 0, err
	}
	deletedCount += count

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return 0, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return deletedCount, nil
}

// deleteEntityRecursive deletes an entity and its children recursively,
// except for children that have relationships with other entities
func (s *EntityService) deleteEntityRecursive(tx *gorm.DB, entityID string) (int, error) {
	// Count of deleted entities
	deletedCount := 0

	// Get all children of the entity
	var children []models.Entity
	if err := tx.Where("parent_id = ?", entityID).Find(&children).Error; err != nil {
		return 0, fmt.Errorf("failed to find children: %w", err)
	}

	// Process each child
	for _, child := range children {
		// Check if the child has relationships with other entities
		var relationshipCount int64
		if err := tx.Model(&models.EntityRelationship{}).
			Where("(from_entity_id = ? OR to_entity_id = ?) AND (from_entity_id != ? AND to_entity_id != ?)",
				child.ID, child.ID, entityID, entityID).
			Count(&relationshipCount).Error; err != nil {
			return 0, fmt.Errorf("failed to check relationships: %w", err)
		}

		// If the child has no relationships with other entities, delete it recursively
		if relationshipCount == 0 {
			count, err := s.deleteEntityRecursive(tx, child.ID)
			if err != nil {
				return 0, err
			}
			deletedCount += count
		}
	}

	// Delete all relationships where this entity is involved
	if err := tx.Where("from_entity_id = ? OR to_entity_id = ?", entityID, entityID).
		Delete(&models.EntityRelationship{}).Error; err != nil {
		return 0, fmt.Errorf("failed to delete relationships: %w", err)
	}

	// Delete the entity
	if err := tx.Delete(&models.Entity{}, "id = ?", entityID).Error; err != nil {
		return 0, fmt.Errorf("failed to delete entity: %w", err)
	}
	deletedCount++

	return deletedCount, nil
}
