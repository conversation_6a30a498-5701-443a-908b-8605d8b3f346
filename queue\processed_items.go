package queue

import (
	"github.com/crawler/db"
	"github.com/crawler/models"
)

// GetProcessedItems returns all completed or failed items in the queue
func (qm *QueueManager) GetProcessedItems() ([]models.QueueItem, error) {
	var items []models.QueueItem
	err := db.DB.Where("status IN ?", []string{"completed", "failed"}).
		Order("updated_at DESC").
		Find(&items).Error
	return items, err
}

// GetProcessedItemsPaginated returns paginated completed or failed items
func (qm *QueueManager) GetProcessedItemsPaginated(page, limit int) ([]models.QueueItem, int64, error) {
	var items []models.QueueItem
	var totalCount int64

	// Get total count
	if err := db.DB.Model(&models.QueueItem{}).Where("status IN ?", []string{"completed", "failed"}).Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get paginated items
	err := db.DB.Where("status IN ?", []string{"completed", "failed"}).
		Order("updated_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&items).Error
	if err != nil {
		return nil, 0, err
	}

	return items, totalCount, nil
}
