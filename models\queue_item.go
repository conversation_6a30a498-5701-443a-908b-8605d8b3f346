package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
)

// QueueItem represents an item in the crawler queue
type QueueItem struct {
	ID        string         `gorm:"primaryKey;type:text;column:id"`
	ParentID  *string        `gorm:"column:parent_id;type:text;index"`
	DataType  string         `gorm:"column:data_type;size:100;not null;index"`
	Data      datatypes.JSON `gorm:"type:jsonb;column:data"`               // Stores the actual data to be processed
	Status    string         `gorm:"column:status;size:50;not null;index"` // pending, processing, completed, failed
	CreatedAt time.Time      `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt time.Time      `gorm:"column:updated_at;autoUpdateTime"`

	// Optional: Track processing attempts and errors
	Attempts     int       `gorm:"column:attempts;default:0"`
	LastAttempt  time.Time `gorm:"column:last_attempt"`
	ErrorMessage string    `gorm:"column:error_message;type:text"`

	// Crawl configuration
	MaxDepth     int  `gorm:"column:max_depth;default:3"`      // Maximum crawl depth (default: 3)
	CurrentDepth int  `gorm:"column:current_depth;default:0"`  // Current depth in the crawl tree
	IsRequeue    bool `gorm:"column:is_requeue;default:false"` // Flag to indicate this is a requeue operation

	// Relationship to parent entity
	Parent *Entity `gorm:"foreignKey:ParentID"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (q *QueueItem) BeforeCreate() error {
	if q.ID == "" {
		q.ID = uuid.New().String()
	}
	return nil
}

// SetData marshals the provided data into the Data field
func (q *QueueItem) SetData(data interface{}) error {
	bytes, err := json.Marshal(data)
	if err != nil {
		return err
	}
	q.Data = datatypes.JSON(bytes)
	return nil
}

// GetData unmarshals the Data field into the provided struct
func (q *QueueItem) GetData(data interface{}) error {
	return json.Unmarshal(q.Data, data)
}

// TableName specifies the table name for GORM
func (QueueItem) TableName() string {
	return "queue_items"
}
