#!/bin/bash

# Exit on error
set -e

# Print commands
set -x

# Build the Docker images
echo "Building Docker images..."
docker-compose build

# Create a volume for the frontend build
echo "Creating frontend build volume..."
docker volume create crawler_frontend_build

# Start the frontend build container
echo "Building frontend..."
docker-compose up -d frontend-build

# Wait for the frontend build to complete
echo "Waiting for frontend build to complete..."
sleep 10

echo "Build completed successfully!"
echo "To start the services, run: docker-compose up -d"
