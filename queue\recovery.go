package queue

import (
	"fmt"
	"time"

	"github.com/crawler/db"
	"github.com/crawler/models"
)

// recoverProcessingItems resets items that were left in the processing state
// This is called when the queue manager is initialized to recover from crashes
func (qm *QueueManager) recoverProcessingItems() {
	// Find all items in the processing state
	var processingItems []models.QueueItem
	if err := db.DB.Where("status = ?", "processing").Find(&processingItems).Error; err != nil {
		fmt.Printf("Error finding processing items for recovery: %v\n", err)
		return
	}

	// If there are any processing items, reset them to pending
	if len(processingItems) > 0 {
		fmt.Printf("Recovering %d items that were left in processing state\n", len(processingItems))
		
		// Update all processing items to pending status
		if err := db.DB.Model(&models.QueueItem{}).Where("status = ?", "processing").Updates(map[string]interface{}{
			"status":        "pending",
			"error_message": "Recovered after process termination",
		}).Error; err != nil {
			fmt.Printf("Error recovering processing items: %v\n", err)
			return
		}
		
		// Log the recovered items
		for _, item := range processingItems {
			fmt.Printf("Recovered queue item: %s (Type: %s, Attempts: %d)\n", item.ID, item.DataType, item.Attempts)
		}
	}
}

// RecoverStuckItems resets items that have been in the processing state for too long
// This can be called periodically to recover items that might be stuck
func (qm *QueueManager) RecoverStuckItems(maxProcessingTime time.Duration) (int, error) {
	// Find items that have been in the processing state for too long
	cutoffTime := time.Now().Add(-maxProcessingTime)
	var stuckItems []models.QueueItem
	
	if err := db.DB.Where("status = ? AND last_attempt < ?", "processing", cutoffTime).Find(&stuckItems).Error; err != nil {
		return 0, fmt.Errorf("error finding stuck items: %w", err)
	}
	
	// If there are any stuck items, reset them to pending
	if len(stuckItems) > 0 {
		fmt.Printf("Recovering %d items that have been stuck in processing state\n", len(stuckItems))
		
		// Update all stuck items to pending status
		if err := db.DB.Model(&models.QueueItem{}).Where("status = ? AND last_attempt < ?", "processing", cutoffTime).Updates(map[string]interface{}{
			"status":        "pending",
			"error_message": fmt.Sprintf("Recovered after being stuck for more than %v", maxProcessingTime),
		}).Error; err != nil {
			return 0, fmt.Errorf("error recovering stuck items: %w", err)
		}
		
		// Log the recovered items
		for _, item := range stuckItems {
			fmt.Printf("Recovered stuck queue item: %s (Type: %s, Attempts: %d)\n", item.ID, item.DataType, item.Attempts)
		}
		
		return len(stuckItems), nil
	}
	
	return 0, nil
}
