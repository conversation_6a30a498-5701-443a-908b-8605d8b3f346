package api

import (
	"time"

	"github.com/crawler/db"
	"github.com/crawler/models"
)

// StatsService handles operations related to statistics
type StatsService struct{}

// NewStatsService creates a new stats service
func NewStatsService() *StatsService {
	return &StatsService{}
}

// GetEntityStats retrieves statistics about entities
func (s *StatsService) GetEntityStats() (*models.EntityStats, error) {
	// Initialize stats
	stats := &models.EntityStats{
		TypeDistribution: make(map[string]int),
	}

	// Get total count
	var totalCount int64
	if err := db.DB.Model(&models.Entity{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}
	stats.TotalCount = int(totalCount)

	// Get type distribution
	var results []struct {
		AssetType string
		Count     int
	}
	if err := db.DB.Model(&models.Entity{}).
		Select("asset_type, count(*) as count").
		Group("asset_type").
		Find(&results).Error; err != nil {
		return nil, err
	}

	for _, result := range results {
		stats.TypeDistribution[result.AssetType] = result.Count
	}

	// Get entities created in the last 24 hours
	now := time.Now()
	last24Hours := now.Add(-24 * time.Hour)
	var last24HoursCount int64
	if err := db.DB.Model(&models.Entity{}).
		Where("created_at >= ?", last24Hours).
		Count(&last24HoursCount).Error; err != nil {
		return nil, err
	}
	stats.CreatedLast24Hours = int(last24HoursCount)

	// Get entities created in the last week
	lastWeek := now.Add(-7 * 24 * time.Hour)
	var lastWeekCount int64
	if err := db.DB.Model(&models.Entity{}).
		Where("created_at >= ?", lastWeek).
		Count(&lastWeekCount).Error; err != nil {
		return nil, err
	}
	stats.CreatedLastWeek = int(lastWeekCount)

	// Get entities created in the last month
	lastMonth := now.Add(-30 * 24 * time.Hour)
	var lastMonthCount int64
	if err := db.DB.Model(&models.Entity{}).
		Where("created_at >= ?", lastMonth).
		Count(&lastMonthCount).Error; err != nil {
		return nil, err
	}
	stats.CreatedLastMonth = int(lastMonthCount)

	return stats, nil
}
