import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  CircularProgress,
  Divider,
  Stack,
  Alert,
  Paper
} from '@mui/material';
import AddQueueItemDialog from '../components/AddQueueItemDialog';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StopIcon from '@mui/icons-material/Stop';
import StorageIcon from '@mui/icons-material/Storage';
import QueueIcon from '@mui/icons-material/Queue';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import RefreshIcon from '@mui/icons-material/Refresh';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout/Layout';
import apiService, { Entity, QueueItem } from '../services/api';
import { useSnackbar } from 'notistack';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [crawlerStatus, setCrawlerStatus] = useState({
    status: 'stopped',
    message: 'Crawler is stopped'
  });
  const [stats, setStats] = useState({
    entities: 0,
    pendingItems: 0,
    processingItems: 0,
  });
  const [addQueueItemDialogOpen, setAddQueueItemDialogOpen] = useState(false);

  useEffect(() => {
    fetchStats();

    // Set up periodic refresh of crawler status
    const intervalId = setInterval(() => {
      // Only refresh the status, not all stats
      apiService.getCrawlerStatus().then(response => {
        setCrawlerStatus(response.data);
      }).catch(error => {
        console.error('Error fetching crawler status:', error);
      });
    }, 5000); // Refresh every 5 seconds

    return () => clearInterval(intervalId); // Clean up on unmount
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const [entitiesRes, pendingRes, processingRes, statusRes] = await Promise.all([
        apiService.getEntities(),
        apiService.getQueuePending(),
        apiService.getQueueProcessing(),
        apiService.getCrawlerStatus(),
      ]);

      setStats({
        entities: entitiesRes.data.length,
        pendingItems: pendingRes.data.length,
        processingItems: processingRes.data.length,
      });

      // Update crawler status
      setCrawlerStatus(statusRes.data);
    } catch (error) {
      console.error('Error fetching stats:', error);
      enqueueSnackbar('Failed to fetch dashboard statistics', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleStartCrawler = async () => {
    try {
      setLoading(true);
      await apiService.startProcessing();
      // Update status immediately for better UX
      setCrawlerStatus({
        status: 'running',
        message: 'Crawler is running'
      });
      enqueueSnackbar('Crawler started successfully', { variant: 'success' });
      // Refresh stats after a short delay
      setTimeout(fetchStats, 1000);
    } catch (error) {
      console.error('Error starting crawler:', error);
      enqueueSnackbar('Failed to start crawler', { variant: 'error' });
      // Refresh stats to get the actual status
      fetchStats();
    } finally {
      setLoading(false);
    }
  };

  const handleStopCrawler = async () => {
    try {
      setLoading(true);
      await apiService.stopProcessing();
      // Update status immediately for better UX
      setCrawlerStatus({
        status: 'stopped',
        message: 'Crawler is stopped'
      });
      enqueueSnackbar('Crawler stopped successfully', { variant: 'success' });
      // Refresh stats after a short delay
      setTimeout(fetchStats, 1000);
    } catch (error) {
      console.error('Error stopping crawler:', error);
      enqueueSnackbar('Failed to stop crawler', { variant: 'error' });
      // Refresh stats to get the actual status
      fetchStats();
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout title="Dashboard">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Crawler System Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Monitor and control your crawler system from this dashboard.
        </Typography>
      </Box>

      {/* Crawler Control */}
      <Paper elevation={0} sx={{ p: 3, mb: 4, border: '1px solid rgba(0, 0, 0, 0.12)', borderRadius: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="h6">Crawler Control</Typography>
          <Button
            size="small"
            startIcon={<RefreshIcon />}
            onClick={() => fetchStats()}
            disabled={loading}
          >
            Refresh Status
          </Button>
        </Box>
        <Divider sx={{ mb: 2 }} />
        <Stack direction="row" spacing={2} alignItems="center">
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box
              sx={{
                width: 12,
                height: 12,
                borderRadius: '50%',
                mr: 1,
                bgcolor:
                  crawlerStatus.status === 'running' ? 'success.main' :
                  crawlerStatus.status === 'waiting' ? 'info.main' :
                  crawlerStatus.status === 'error' ? 'error.main' : 'text.disabled'
              }}
            />
            <Box>
              <Typography>
                Status: <strong>{crawlerStatus.status.charAt(0).toUpperCase() + crawlerStatus.status.slice(1)}</strong>
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {crawlerStatus.message}
              </Typography>
            </Box>
          </Box>
          <Button
            variant="contained"
            color={crawlerStatus.status === 'running' || crawlerStatus.status === 'waiting' ? 'error' : 'success'}
            startIcon={crawlerStatus.status === 'running' || crawlerStatus.status === 'waiting' ? <StopIcon /> : <PlayArrowIcon />}
            onClick={crawlerStatus.status === 'running' || crawlerStatus.status === 'waiting' ? handleStopCrawler : handleStartCrawler}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> :
              (crawlerStatus.status === 'running' || crawlerStatus.status === 'waiting' ? 'Stop Crawler' : 'Start Crawler')}
          </Button>
        </Stack>
      </Paper>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <StorageIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Entities</Typography>
              </Box>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {loading ? <CircularProgress /> : stats.entities}
              </Typography>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => navigate('/entities')}
              >
                View Entities
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <QueueIcon color="secondary" sx={{ mr: 1 }} />
                <Typography variant="h6">Queue Items</Typography>
              </Box>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {loading ? <CircularProgress /> : (stats.pendingItems + stats.processingItems)}
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="body2">
                  Pending: {stats.pendingItems}
                </Typography>
                <Typography variant="body2">
                  Processing: {stats.processingItems}
                </Typography>
              </Box>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => navigate('/queue')}
              >
                Manage Queue
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <AccountTreeIcon color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Graph Visualization</Typography>
              </Box>
              <Box sx={{ height: '100px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Button
                  variant="contained"
                  color="info"
                  size="large"
                  onClick={() => navigate('/graph')}
                >
                  Explore Graph
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Paper elevation={0} sx={{ p: 3, border: '1px solid rgba(0, 0, 0, 0.12)', borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          Quick Actions
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              onClick={() => setAddQueueItemDialogOpen(true)}
            >
              Add Queue Item
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              onClick={() => navigate('/entities')}
            >
              View All Entities
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              onClick={() => navigate('/graph/roots')}
            >
              View Root Entities
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              onClick={() => navigate('/stats')}
            >
              View Statistics
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              onClick={() => fetchStats()}
            >
              Refresh Stats
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Add Queue Item Dialog */}
      <AddQueueItemDialog
        open={addQueueItemDialogOpen}
        onClose={() => setAddQueueItemDialogOpen(false)}
        onSuccess={fetchStats}
      />
    </Layout>
  );
};

export default Dashboard;
