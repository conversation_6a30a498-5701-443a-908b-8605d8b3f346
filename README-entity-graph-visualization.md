# Entity Graph Visualization with Type Grouping

This feature allows you to visualize entities and their relationships in an interactive graph, with the ability to group entities by their type.

## Features

### Entity Graph Visualization

- Interactive force-directed graph showing entities and their relationships
- Entities are represented as nodes with labels
- Relationships are represented as directed links with optional labels
- Nodes are color-coded by entity type
- Click on nodes to see detailed information
- Drag nodes to rearrange the graph
- Zoom and pan to navigate the graph

### Group by Entity Type

The "Group by Entity Type" feature organizes entities of the same type into clusters, making it easier to understand the structure of your data.

#### How to Use

1. Navigate to the Graph page
2. Toggle the "Group by Entity Type" switch to enable grouping (enabled by default)
3. Adjust the "Grouping Strength" slider to control how tightly entities are grouped
   - Higher values create tighter clusters
   - Lower values allow more natural positioning

#### Benefits

- Quickly identify patterns and relationships between different entity types
- Understand the composition of your data at a glance
- Easily spot outliers and unusual connections
- Better visualize the hierarchy and structure of your data

### Color Coding

Entities are color-coded by type for easy identification:

- Domain: Blue (#4285F4)
- IP: Red (#EA4335)
- DNS: Yellow (#FBBC05)
- ASN: Green (#34A853)
- Organization: Purple (#8E44AD)
- Email: Orange (#F39C12)
- WHOIS: Teal (#1ABC9C)
- Contact: Bright Red (#E74C3C)
- DNS Record: Light Blue (#3498DB)
- IP Range: Dark Orange (#D35400)
- Technology: Bright Green (#2ECC71)
- Social: Violet (#9B59B6)
- Application: Orange (#E67E22)
- Phone: Dark Teal (#16A085)
- Subdomain: Dark Blue (#2980B9)
- Nameserver: Dark Red (#C0392B)

### Additional Controls

- **Node Repulsion**: Adjust how strongly nodes repel each other
- **Search Filters**: Filter by entity type, name, and relationship depth
- **View Options**: View all entities, root entities, or focus on specific entity types

## Technical Implementation

The grouping is implemented using a custom force in the force-directed graph simulation:

1. Entities are first categorized by type
2. A central point is calculated for each type group
3. A custom force pulls entities toward their type's central point
4. The strength of this force is adjustable via the UI

This creates a natural clustering effect while still allowing the force-directed layout to optimize the overall graph structure.

## Tips for Large Graphs

- Use filters to reduce the number of displayed entities
- Adjust the depth parameter to control how many levels of relationships are shown
- Use the "Group by Entity Type" feature to organize complex graphs
- Drag important nodes to fixed positions to create a more stable layout
- Use the search function to find specific entities in large graphs
