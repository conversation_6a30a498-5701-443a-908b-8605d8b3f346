# Queue Item Data as String

This document explains the changes made to handle queue item data as a plain string rather than JSON.

## Changes Made

1. **Queue Item Form**
   - Updated the data field label from "Data (JSON)" to "Data (String)"
   - Removed JSON validation from the form
   - Added a helpful message to indicate that data should be entered as a plain string
   - Updated the button validation to check for empty data

2. **Data Templates**
   - Changed the template data for each entity type from JSON objects to simple strings
   - For example, domain template is now just "example.com" instead of `{"name": "example.com"}`

3. **Data Handling**
   - Modified the `handleAddItem` function to use the string data directly without parsing it as JSON
   - Added validation to ensure the data field is not empty

4. **Data Display**
   - Updated the `renderQueueItemData` function to display data as a string without attempting to parse it as JSON
   - This ensures that the data is displayed exactly as it was entered

## How to Use

When adding a new queue item:

1. Select the appropriate data type from the dropdown
2. Enter the data as a plain string (not JSON)
   - For domains: Enter the domain name (e.g., "example.com")
   - For IPs: Enter the IP address (e.g., "***********")
   - For IP ranges: Enter the CIDR notation (e.g., "***********/24")
   - For ASNs: Enter the ASN number (e.g., "12345")
   - For emails: Enter the email address (e.g., "<EMAIL>")
   - For organizations: Enter the organization name (e.g., "Example Organization")

3. Optionally enter a parent ID
4. Click "Add" to submit the queue item

## Examples

### Domain
```
example.com
```

### IP Address
```
***********
```

### IP Range
```
***********/24
```

### ASN
```
12345
```

### Email
```
<EMAIL>
```

### Organization
```
Example Organization
```

## Technical Details

The backend API expects the queue item data to be a string, not a JSON object. The frontend now correctly sends the data as a string without attempting to parse it as JSON.

This change ensures that the data is handled consistently throughout the application and matches the expected format in the backend.
