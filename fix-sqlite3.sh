#!/bin/bash

# This script fixes compilation issues with the go-sqlite3 package
# by replacing 64-bit file offset functions with their standard counterparts.

set -e

echo "Fixing go-sqlite3 package..."

# Get the go-sqlite3 package directory
SQLITE3_DIR=$(go list -f '{{.Dir}}' github.com/mattn/go-sqlite3)

if [ -z "$SQLITE3_DIR" ]; then
    echo "Error: go-sqlite3 package not found. Please run 'go get github.com/mattn/go-sqlite3' first."
    exit 1
fi

echo "Found go-sqlite3 package at: $SQLITE3_DIR"

# Fix the pread64 function
echo "Fixing pread64 function..."
sed -i 's/{ "pread64",      (sqlite3_syscall_ptr)pread64,    0  },/{ "pread64",      (sqlite3_syscall_ptr)pread,    0  },/g' "$SQLITE3_DIR/sqlite3-binding.c"

# Fix the pwrite64 function
echo "Fixing pwrite64 function..."
sed -i 's/{ "pwrite64",     (sqlite3_syscall_ptr)pwrite64,   0  },/{ "pwrite64",     (sqlite3_syscall_ptr)pwrite,   0  },/g' "$SQLITE3_DIR/sqlite3-binding.c"

# Fix the off64_t type
echo "Fixing off64_t type..."
sed -i 's/off64_t/off_t/g' "$SQLITE3_DIR/sqlite3-binding.c"

echo "Fixed go-sqlite3 package successfully!"
echo "Now you can build your application with CGO_ENABLED=1"
