@echo off
setlocal enabledelayedexpansion

REM Script to test API endpoints

REM Base URL
set BASE_URL=http://localhost

REM Test health endpoint
echo Testing health endpoint...
curl -v "%BASE_URL%/health"
echo.
echo.

REM Test API health endpoint
echo Testing API health endpoint...
curl -v "%BASE_URL%/api/health"
echo.
echo.

REM Test direct API health endpoint
echo Testing direct API health endpoint...
curl -v "http://localhost:8080/health"
echo.
echo.

REM Test entities endpoint
echo Testing entities endpoint...
curl -v "%BASE_URL%/api/entities"
echo.
echo.

REM Test queue endpoint
echo Testing queue endpoint...
curl -v "%BASE_URL%/api/queue"
echo.
echo.

REM Test blacklist endpoint
echo Testing blacklist endpoint...
curl -v "%BASE_URL%/api/blacklist"
echo.
echo.

REM Test graph endpoint
echo Testing graph endpoint...
curl -v "%BASE_URL%/api/graph"
echo.
echo.

REM Test crawler endpoint
echo Testing crawler endpoint...
curl -v "%BASE_URL%/api/crawler/status"
echo.
echo.

echo API tests completed.
