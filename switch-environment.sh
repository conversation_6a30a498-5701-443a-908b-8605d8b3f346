#!/bin/bash

# Script to switch between development and production environments

# Display usage information
function show_usage {
  echo "Usage: $0 [option]"
  echo "Options:"
  echo "  dev       - Use development environment (default)"
  echo "  prod      - Use production environment"
  echo "  help      - Show this help message"
}

# Check if an option was provided
if [ $# -eq 0 ]; then
  show_usage
  exit 1
fi

# Process the option
case "$1" in
  dev)
    echo "Switching to development environment..."
    echo "Stopping any running containers..."
    docker-compose down

    echo "Starting development environment..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

    echo "Development environment is now running."
    echo "You can access the application at http://localhost"
    echo "The API is directly accessible at http://localhost:8080 for debugging"
    ;;
  prod)
    echo "Switching to production environment..."
    echo "Stopping any running containers..."
    docker-compose down

    echo "Starting production environment..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

    echo "Production environment is now running."
    echo "You can access the application at http://localhost"
    echo "The API is accessible through the frontend at http://localhost/api"
    ;;
  help)
    show_usage
    exit 0
    ;;
  *)
    echo "Error: Unknown option '$1'"
    show_usage
    exit 1
    ;;
esac
