@echo off
setlocal enabledelayedexpansion

REM This script applies a patch to the go-sqlite3 package to fix compilation issues
REM with 64-bit file offset functions on some systems.

REM Check if patch command is available
where patch >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: 'patch' command not found. Please install Git for Windows or a similar tool that provides the patch command.
    exit /b 1
)

REM Get the go-sqlite3 package directory
for /f "tokens=*" %%a in ('go list -f "{{.Dir}}" github.com/mattn/go-sqlite3') do set SQLITE3_DIR=%%a

if "!SQLITE3_DIR!" == "" (
    echo Error: go-sqlite3 package not found. Please run 'go get github.com/mattn/go-sqlite3' first.
    exit /b 1
)

echo Found go-sqlite3 package at: !SQLITE3_DIR!

REM Apply the patch
echo Applying patch to fix 64-bit file offset functions...
patch -p1 -d "!SQLITE3_DIR!" < "%~dp0sqlite3-fix.patch"

if %ERRORLEVEL% neq 0 (
    echo Error: Failed to apply patch.
    exit /b 1
)

echo Patch applied successfully!
echo Now you can build your application with CGO_ENABLED=1
