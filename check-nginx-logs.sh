#!/bin/bash

# Script to check Nginx logs for debugging

# Display usage information
function show_usage {
  echo "Usage: $0 [option]"
  echo "Options:"
  echo "  frontend  - Check frontend Nginx logs"
  echo "  api       - Check API Nginx logs"
  echo "  all       - Check all Nginx logs"
  echo "  help      - Show this help message"
}

# Check if an option was provided
if [ $# -eq 0 ]; then
  show_usage
  exit 1
fi

# Process the option
case "$1" in
  frontend)
    echo "Checking frontend Nginx logs..."
    docker-compose exec frontend tail -f /var/log/nginx/error.log
    ;;
  api)
    echo "Checking API Nginx logs..."
    docker-compose exec nginx-api tail -f /var/log/nginx/error.log
    ;;
  all)
    echo "Checking all Nginx logs..."
    echo "Frontend Nginx error log:"
    docker-compose exec frontend tail -n 20 /var/log/nginx/error.log
    echo ""
    echo "Frontend Nginx access log:"
    docker-compose exec frontend tail -n 20 /var/log/nginx/access.log
    echo ""
    echo "API Nginx error log:"
    docker-compose exec nginx-api tail -n 20 /var/log/nginx/error.log
    echo ""
    echo "API Nginx access log:"
    docker-compose exec nginx-api tail -n 20 /var/log/nginx/access.log
    ;;
  help)
    show_usage
    exit 0
    ;;
  *)
    echo "Error: Unknown option '$1'"
    show_usage
    exit 1
    ;;
esac
