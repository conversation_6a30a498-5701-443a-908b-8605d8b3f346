import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
} from '@mui/material';
import { Delete as DeleteIcon, Add as AddIcon } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import Layout from '../components/Layout/Layout';
import apiService, { BlacklistedEntity } from '../services/api';

const BlacklistPage: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [blacklist, setBlacklist] = useState<BlacklistedEntity[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedEntity, setSelectedEntity] = useState<BlacklistedEntity | null>(null);
  const [newEntity, setNewEntity] = useState({
    asset_type: '',
    asset_name: '',
    pattern: '',
    description: '',
  });
  const [assetTypeFilter, setAssetTypeFilter] = useState('');
  const [assetNameFilter, setAssetNameFilter] = useState('');

  useEffect(() => {
    fetchBlacklist();
  }, [assetTypeFilter, assetNameFilter]);

  const fetchBlacklist = async () => {
    try {
      setLoading(true);
      const params: { asset_type?: string; asset_name?: string } = {};
      if (assetTypeFilter) params.asset_type = assetTypeFilter;
      if (assetNameFilter) params.asset_name = assetNameFilter;

      const response = await apiService.getBlacklist(params);
      setBlacklist(response.data);
    } catch (error) {
      console.error('Error fetching blacklist:', error);
      enqueueSnackbar('Failed to fetch blacklist', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleAddEntity = async () => {
    if (!newEntity.asset_type) {
      enqueueSnackbar('Asset type is required', { variant: 'error' });
      return;
    }

    try {
      setLoading(true);
      await apiService.addBlacklistedEntity(newEntity);
      enqueueSnackbar('Entity added to blacklist', { variant: 'success' });
      setAddDialogOpen(false);
      setNewEntity({
        asset_type: '',
        asset_name: '',
        pattern: '',
        description: '',
      });
      fetchBlacklist();
    } catch (error) {
      console.error('Error adding entity to blacklist:', error);
      enqueueSnackbar('Failed to add entity to blacklist', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = (entity: BlacklistedEntity) => {
    setSelectedEntity(entity);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedEntity) return;

    try {
      setLoading(true);
      await apiService.removeBlacklistedEntity(selectedEntity.id);
      enqueueSnackbar('Entity removed from blacklist', { variant: 'success' });
      fetchBlacklist();
    } catch (error) {
      console.error('Error removing entity from blacklist:', error);
      enqueueSnackbar('Failed to remove entity from blacklist', { variant: 'error' });
    } finally {
      setLoading(false);
      setDeleteDialogOpen(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Asset type options for the dropdown
  const assetTypes = [
    'domain',
    'whois',
    'contact',
    'ip',
    'dns',
    'asn',
    'org',
    'dnsrecord',
    'iprange',
    'email',
    'tech',
    'social',
    'app',
    'phone',
    'subdomain',
    'nameserver',
  ];

  return (
    <Layout title="Blacklist">
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Blacklist Management
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Manage entities that should not be crawled.
        </Typography>

        {/* Filters and Actions */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
                <FormControl sx={{ width: 300 }} size="small">
                  <InputLabel>Filter by Asset Type</InputLabel>
                  <Select
                    value={assetTypeFilter}
                    onChange={(e) => setAssetTypeFilter(e.target.value)}
                    label="Filter by Asset Type"
                    MenuProps={{
                      PaperProps: {
                        style: {
                          maxHeight: 300
                        }
                      }
                    }}
                  >
                    <MenuItem value="">All Types</MenuItem>
                    {assetTypes.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Filter by Asset Name"
                value={assetNameFilter}
                onChange={(e) => setAssetNameFilter(e.target.value)}
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setAddDialogOpen(true)}
                fullWidth
              >
                Add to Blacklist
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Blacklist Table */}
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Asset Type</TableCell>
                  <TableCell>Asset Name</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Created At</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading && blacklist.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      <CircularProgress size={24} />
                    </TableCell>
                  </TableRow>
                ) : blacklist.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      No blacklisted entities found
                    </TableCell>
                  </TableRow>
                ) : (
                  blacklist
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((entity) => (
                      <TableRow key={entity.id}>
                        <TableCell>
                          <Chip
                            label={entity.asset_type}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>{entity.asset_name || '(Any)'}</TableCell>
                        <TableCell>{entity.description}</TableCell>
                        <TableCell>{formatDate(entity.created_at)}</TableCell>
                        <TableCell>
                          <IconButton
                            color="error"
                            onClick={() => handleDeleteClick(entity)}
                            size="small"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={blacklist.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      </Box>

      {/* Add Entity Dialog */}
      <Dialog open={addDialogOpen} onClose={() => setAddDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Entity to Blacklist</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
                <FormControl required sx={{ width: 300 }}>
                  <InputLabel>Asset Type</InputLabel>
                  <Select
                    value={newEntity.asset_type}
                    onChange={(e) => setNewEntity({ ...newEntity, asset_type: e.target.value })}
                    label="Asset Type"
                    MenuProps={{
                      PaperProps: {
                        style: {
                          maxHeight: 300
                        }
                      }
                    }}
                    sx={{
                      '& .MuiSelect-select': {
                        padding: '12px 14px',
                        fontSize: '1rem'
                      }
                    }}
                  >
                    {assetTypes.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Asset Name (leave empty to block entire type)"
                value={newEntity.asset_name}
                onChange={(e) => setNewEntity({ ...newEntity, asset_name: e.target.value })}
                helperText="Specific entity name to block, or leave empty to block all entities of this type"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={newEntity.description}
                onChange={(e) => setNewEntity({ ...newEntity, description: e.target.value })}
                multiline
                rows={2}
                helperText="Optional: Reason for blacklisting"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleAddEntity}
            color="primary"
            disabled={!newEntity.asset_type || loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Removal</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to remove{' '}
            {selectedEntity ? (
              <>
                <strong>{selectedEntity.asset_type}</strong>
                {selectedEntity.asset_name && (
                  <>
                    {' '}
                    - <strong>{selectedEntity.asset_name}</strong>
                  </>
                )}
              </>
            ) : (
              'this entity'
            )}{' '}
            from the blacklist?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">
            {loading ? <CircularProgress size={24} /> : 'Remove'}
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default BlacklistPage;
