package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/crawler/api"
	"github.com/crawler/config"
	"github.com/crawler/crawler"
	"github.com/crawler/db"
	"github.com/crawler/shutdown"
	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Connect to database
	database, err := db.Connect(cfg.DB)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Run migrations
	if err := db.Migrate(database); err != nil {
		log.Fatalf("Failed to run migrations: %v", err)
	}

	// Create crawler based on mode
	var crawlerInstance api.CrawlerInterface
	var router *gin.Engine

	if cfg.Mode == "slave" {
		// Slave mode
		if cfg.MasterURL == "" {
			log.Fatalf("Master URL is required in slave mode")
		}
		if cfg.APIKey == "" {
			log.Fatalf("API key is required in slave mode")
		}

		// Create crawler slave
		crawlerSlave := crawler.NewCrawler(cfg.WorkerPool)

		// Register processors
		crawlerSlave.RegisterProcessor("domain", &crawler.SampleProcessor{})
		crawlerSlave.RegisterProcessor("ip", &crawler.SampleProcessor{})
		crawlerSlave.RegisterProcessor("iprange", &crawler.SampleProcessor{})
		crawlerSlave.RegisterProcessor("asn", &crawler.SampleProcessor{})
		crawlerSlave.RegisterProcessor("email", &crawler.SampleProcessor{})
		crawlerSlave.RegisterProcessor("org", &crawler.SampleProcessor{})

		// Auto-start crawler if configured
		if cfg.AutoStartCrawler {
			log.Println("Auto-starting crawler slave...")
			if err := crawlerSlave.Start(); err != nil {
				log.Printf("Warning: Failed to auto-start crawler slave: %v", err)
			} else {
				log.Println("Crawler slave started successfully")
			}
		}

		crawlerInstance = crawlerSlave
		router = api.SetupRouter(crawlerSlave)
	} else {
		// Master mode
		crawlerMaster := crawler.NewCrawler(cfg.WorkerPool)

		// Register processors
		crawlerMaster.RegisterProcessor("domain", &crawler.SampleProcessor{})
		crawlerMaster.RegisterProcessor("ip", &crawler.SampleProcessor{})
		crawlerMaster.RegisterProcessor("iprange", &crawler.SampleProcessor{})
		crawlerMaster.RegisterProcessor("asn", &crawler.SampleProcessor{})
		crawlerMaster.RegisterProcessor("email", &crawler.SampleProcessor{})
		crawlerMaster.RegisterProcessor("org", &crawler.SampleProcessor{})

		// Auto-start crawler if configured
		if cfg.AutoStartCrawler {
			log.Println("Auto-starting crawler master...")
			if err := crawlerMaster.Start(); err != nil {
				log.Printf("Warning: Failed to auto-start crawler master: %v", err)
			} else {
				log.Println("Crawler master started successfully")
			}
		}

		crawlerInstance = crawlerMaster
		router = api.SetupRouter(crawlerMaster)
	}

	// Create static directory if it doesn't exist
	staticDir := "static"
	if _, err := os.Stat(staticDir); os.IsNotExist(err) {
		if err := os.MkdirAll(staticDir, 0755); err != nil {
			log.Fatalf("Failed to create static directory: %v", err)
		}
	}

	// Serve static files
	router.Static("/static", staticDir)
	router.StaticFile("/", filepath.Join(staticDir, "index.html"))

	// Create a server with graceful shutdown
	server := &http.Server{
		Addr:    fmt.Sprintf(":%s", cfg.Port),
		Handler: router,
	}

	// Create a context that will be canceled on shutdown signal
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Set up graceful shutdown
	shutdownChan := make(chan struct{})
	go shutdown.HandleSignals(ctx, cancel, shutdownChan, crawlerInstance)

	// Start the server
	go func() {
		log.Printf("Starting server on port %s in %s mode", cfg.Port, cfg.Mode)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for shutdown signal
	<-ctx.Done()

	// Create a timeout context for server shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer shutdownCancel()

	// Shutdown the server
	log.Println("Shutting down HTTP server...")
	if err := server.Shutdown(shutdownCtx); err != nil {
		log.Printf("Error shutting down server: %v", err)
	}

	// Wait for shutdown to complete
	<-shutdownChan

	// Close database connection
	sqlDB, err := db.DB.DB()
	if err == nil {
		log.Println("Closing database connection...")
		if err := sqlDB.Close(); err != nil {
			log.Printf("Error closing database connection: %v", err)
		}
	}

	log.Println("Server shutdown complete")
}
