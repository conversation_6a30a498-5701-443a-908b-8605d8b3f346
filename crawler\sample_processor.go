package crawler

import (
	"fmt"

	"github.com/crawler/db"
	"github.com/crawler/models"
	"github.com/google/uuid"
)

// SampleProcessor is an example implementation of a DataProcessor
type SampleProcessor struct{}

// Process implements the DataProcessor interface
func (p *SampleProcessor) Process(item *models.QueueItem) ([]*models.QueueItem, *models.Entity, []*models.EntityRelationship, error) {
	// This is a sample implementation
	// In a real-world scenario, you would:
	// 1. Extract data from the queue item
	// 2. Process the data (e.g., make API calls, scrape websites, etc.)
	// 3. Create entities and relationships based on the processed data
	// 4. Generate new queue items for further crawling

	// For demonstration, let's create a simple entity
	var data map[string]interface{}
	if err := item.GetData(&data); err != nil {
		return nil, nil, nil, fmt.Errorf("failed to parse item data: %w", err)
	}

	// Check if an entity with this name already exists
	name := fmt.Sprintf("%v", data["name"])
	var existingEntity models.Entity
	result := db.DB.Where("asset_type = ? AND asset_name = ?", item.DataType, name).First(&existingEntity)

	// If entity already exists, use it instead of creating a new one
	var entity *models.Entity
	if result.Error == nil {
		// Entity already exists
		entity = &existingEntity

		// Update attributes if needed
		if err := entity.SetAttributes(data); err != nil {
			return nil, nil, nil, fmt.Errorf("failed to update entity attributes: %w", err)
		}
	} else {
		// Create a new entity
		entity = &models.Entity{
			ID:        uuid.New().String(),
			AssetType: item.DataType,
			AssetName: name,
			DoFollow:  true, // Default to true, can be overridden via API
		}

		// If this item has a parent, set the parent ID
		if item.ParentID != nil {
			entity.ParentID = item.ParentID
		}

		// Check if do_follow is specified in the data
		if doFollow, ok := data["do_follow"].(bool); ok {
			entity.DoFollow = doFollow
		}

		// Set attributes
		if err := entity.SetAttributes(data); err != nil {
			return nil, nil, nil, fmt.Errorf("failed to set entity attributes: %w", err)
		}
	}

	// Create relationships (if applicable)
	var relationships []*models.EntityRelationship
	if item.ParentID != nil {
		// Check if relationship already exists
		var count int64
		db.DB.Model(&models.EntityRelationship{}).Where(
			"from_entity_id = ? AND to_entity_id = ? AND label = ?",
			*item.ParentID, entity.ID, "has",
		).Count(&count)

		if count == 0 {
			// Relationship doesn't exist, create it
			relationship := &models.EntityRelationship{
				FromEntityID: *item.ParentID,
				ToEntityID:   entity.ID,
				Label:        "has",
			}
			relationships = append(relationships, relationship)
		}
	}

	// Generate new queue items for further crawling
	var newItems []*models.QueueItem

	// Example: If the data contains a "children" field, create queue items for each child
	if children, ok := data["children"].([]interface{}); ok {
		for _, child := range children {
			if childMap, ok := child.(map[string]interface{}); ok {
				// Check if this child already exists
				childName, _ := childMap["name"].(string)
				var childCount int64
				db.DB.Model(&models.Entity{}).Where(
					"asset_type = ? AND asset_name = ?",
					"child", childName,
				).Count(&childCount)

				// Only create queue item if child doesn't exist
				if childCount == 0 {
					// Create a new queue item
					childItem := &models.QueueItem{
						ID:       uuid.New().String(),
						DataType: "child",
						Status:   "pending",
					}

					// Set parent ID if entity exists
					if entity != nil {
						childItem.ParentID = &entity.ID
					}

					// Set data
					if err := childItem.SetData(childMap); err != nil {
						return nil, nil, nil, fmt.Errorf("failed to set item data: %w", err)
					}
					newItems = append(newItems, childItem)
				}
			}
		}
	}

	return newItems, entity, relationships, nil
}
