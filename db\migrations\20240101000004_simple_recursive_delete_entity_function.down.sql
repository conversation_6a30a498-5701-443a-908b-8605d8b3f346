-- Revert to the previous version of the function
CREATE OR REPLACE FUNCTION delete_entity_with_children(
    p_entity_id TEXT
)
RETURNS TABLE(deleted_count INT) AS $$
DECLARE
    v_count INT := 0;
    v_child RECORD;
    v_preserved_count INT := 0;
    v_entities_to_preserve TEXT[];
BEGIN
    -- Check if entity exists
    IF NOT EXISTS (SELECT 1 FROM entities WHERE id = p_entity_id) THEN
        RETURN QUERY SELECT v_count;
        RETURN;
    END IF;

    -- Build a complete entity tree starting from the target entity
    WITH RECURSIVE entity_tree AS (
        -- Start with the entity itself
        SELECT 
            id, 
            parent_id,
            ARRAY[id] AS path,
            0 AS level
        FROM entities
        WHERE id = p_entity_id
        
        UNION ALL
        
        -- Add all descendants
        SELECT 
            e.id, 
            e.parent_id,
            et.path || e.id,
            et.level + 1
        FROM entities e
        JOIN entity_tree et ON e.parent_id = et.id
    ),
    -- Find all entities in the tree that have relationships with entities outside the tree
    entities_with_external_relations AS (
        SELECT DISTINCT et.id
        FROM entity_tree et
        JOIN entity_relationships er ON (er.from_entity_id = et.id OR er.to_entity_id = et.id)
        WHERE 
            -- The other entity in the relationship is not in the deletion tree
            (
                (er.from_entity_id NOT IN (SELECT id FROM entity_tree) AND
                 er.from_entity_id IS NOT NULL)
                OR
                (er.to_entity_id NOT IN (SELECT id FROM entity_tree) AND
                 er.to_entity_id IS NOT NULL)
            )
    ),
    -- Find all ancestors of entities with external relations
    -- These need to be preserved to maintain the path to the preserved entities
    ancestors_to_preserve AS (
        SELECT DISTINCT unnest(et.path) AS id
        FROM entity_tree et
        WHERE et.id IN (SELECT id FROM entities_with_external_relations)
    )
    -- Combine entities with external relations and their ancestors
    SELECT array_agg(DISTINCT id) INTO v_entities_to_preserve
    FROM (
        SELECT id FROM entities_with_external_relations
        UNION
        SELECT id FROM ancestors_to_preserve
    ) AS entities_to_preserve;

    -- If the entity itself has external relationships or is an ancestor of an entity with external relationships,
    -- we can't delete it
    IF v_entities_to_preserve IS NOT NULL AND p_entity_id = ANY(v_entities_to_preserve) THEN
        RAISE NOTICE 'Entity % has external relationships or is an ancestor of an entity with external relationships', p_entity_id;
        RETURN QUERY SELECT v_count;
        RETURN;
    END IF;

    -- For direct children that need to be preserved, update their parent references
    UPDATE entities
    SET parent_id = NULL
    WHERE parent_id = p_entity_id
    AND (v_entities_to_preserve IS NOT NULL AND id = ANY(v_entities_to_preserve));
    
    -- Count how many direct children were preserved
    GET DIAGNOSTICS v_preserved_count = ROW_COUNT;
    RAISE NOTICE 'Preserved % direct children of entity %', v_preserved_count, p_entity_id;

    -- Process children that don't need to be preserved (depth-first approach)
    FOR v_child IN (
        SELECT id FROM entities 
        WHERE parent_id = p_entity_id
        AND (v_entities_to_preserve IS NULL OR id <> ALL(v_entities_to_preserve))
    ) LOOP
        -- Delete child recursively
        v_count := v_count + (SELECT * FROM delete_entity_with_children(v_child.id));
    END LOOP;

    -- Delete all relationships where this entity is involved
    DELETE FROM entity_relationships
    WHERE from_entity_id = p_entity_id OR to_entity_id = p_entity_id;

    -- Delete the entity
    DELETE FROM entities WHERE id = p_entity_id;
    
    -- Increment count for this entity
    v_count := v_count + 1;

    -- Return the total count of deleted entities
    RETURN QUERY SELECT v_count;
END;
$$ LANGUAGE plpgsql;
