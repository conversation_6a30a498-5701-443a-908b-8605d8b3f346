# Entity Type Grouping Feature

This feature allows you to visualize entities grouped by their type, creating a hierarchical view that makes it easier to understand the structure of your data.

## How to Use

1. Navigate to the Graph page
2. Select the "Entity Types" tab
3. The graph will display entity types as hexagonal nodes
4. Click on a type node to expand/collapse its entities
   - Green indicator: Type is collapsed (click to expand)
   - Red indicator: Type is expanded (click to collapse)
5. Use the type chips to quickly expand/collapse specific entity types
6. Use the "Expand All" and "Collapse All" buttons to manage the view

## Features

### Type Nodes

- Displayed as hexagons with a gradient fill based on the entity type color
- Show the count of entities of each type
- Have an expand/collapse indicator in the center:
  - Green: Type is collapsed (click to expand)
  - Red: Type is expanded (click to collapse)

### Entity Nodes

- Only visible when their type is expanded
- Connected to their type node with a "contains" relationship
- Relationships between entities of the same type are preserved
- Color-coded based on their entity type

### Type Relationships

- Type nodes are connected to each other if there are relationships between their entities
- These connections help visualize the high-level structure of your data

## Benefits

- **Simplified Visualization**: Reduces visual clutter by grouping entities by type
- **Hierarchical View**: Creates a natural hierarchy that's easy to navigate
- **Focus on Specific Types**: Expand only the types you're interested in
- **Better Overview**: Quickly understand the composition of your data
- **Relationship Patterns**: Easily identify patterns between different entity types

## Implementation Details

The grouping is implemented using a hierarchical transformation of the graph data:

1. Entity nodes are grouped by their type
2. Type nodes are created to represent each entity type
3. When a type node is expanded:
   - Its entities are added to the graph
   - Links are created from the type node to each entity
   - Links between entities of the same type are preserved
4. Links between type nodes represent relationships between entities of different types

## Tips for Large Graphs

- Start with all types collapsed to get an overview of the data structure
- Expand only the types you're interested in to avoid visual clutter
- Use the type chips to quickly toggle specific entity types
- The hierarchical view is especially useful for large datasets with many entity types
