package models

import (
	"time"
)

// EntityRelationship corresponds to the 'entity_relationships' table.
type EntityRelationship struct {
	RelationshipID int64     `gorm:"primaryKey;autoIncrement;column:relationship_id"`
	FromEntityID   string    `gorm:"column:from_entity_id;not null;type:text;index"`
	ToEntityID     string    `gorm:"column:to_entity_id;not null;type:text;index"`
	Label          string    `gorm:"column:label;type:text;index"`
	Style          string    `gorm:"column:style;size:50"`
	Color          string    `gorm:"column:color;size:50"`
	CreatedAt      time.Time `gorm:"column:created_at;autoCreateTime"`

	FromEntity *Entity `gorm:"foreignKey:FromEntityID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	ToEntity   *Entity `gorm:"foreignKey:ToEntityID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
}

// TableName specifies the table name for GORM.
func (EntityRelationship) TableName() string {
	return "entity_relationships"
}
