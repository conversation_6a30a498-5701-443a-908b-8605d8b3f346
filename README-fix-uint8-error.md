# Fix for "to a uint8: invalid syntax" Error

This document explains the changes made to fix the "to a uint8: invalid syntax" error that occurred when using the PostgreSQL-based entity relationship graph query.

## Issue

When executing the PostgreSQL function `get_entity_graph` and trying to scan the result into a byte slice, the following error occurred:

```
to a uint8: invalid syntax
```

This error was caused by a type mismatch between the PostgreSQL function's return type (JSONB) and the Go variable used to receive the result ([]byte).

## Solution

The issue was fixed by making the following changes:

### 1. Changed the PostgreSQL Function Return Type

Modified the PostgreSQL function to return TEXT instead of JSONB:

```sql
-- Before
CREATE OR REPLACE FUNCTION get_entity_graph(
    p_filters JSONB,
    p_max_depth INT DEFAULT 1
)
RETURNS JSONB AS $$

-- After
CREATE OR REPLACE FUNCTION get_entity_graph(
    p_filters JSONB,
    p_max_depth INT DEFAULT 1
)
RETURNS TEXT AS $$
```

And updated the return statement to convert JSONB to TEXT:

```sql
-- Before
RETURN v_result;

-- After
RETURN v_result::TEXT;
```

### 2. Updated the Go Code to Handle String Results

Modified the Go code to scan the result into a string variable instead of a byte slice:

```go
// Before
var result []byte
query := `SELECT get_entity_graph($1, $2)`
err = db.DB.Raw(query, string(filtersJSON), depth).Scan(&result).Error

// After
var resultStr string
query := `SELECT get_entity_graph($1, $2)`
err = db.DB.Raw(query, string(filtersJSON), depth).Scan(&resultStr).Error
```

And updated the JSON unmarshaling to convert the string to bytes:

```go
// Before
if err := json.Unmarshal(result, &graphData); err != nil {
    return nil, fmt.Errorf("failed to unmarshal graph data: %w", err)
}

// After
if err := json.Unmarshal([]byte(resultStr), &graphData); err != nil {
    return nil, fmt.Errorf("failed to unmarshal graph data: %w", err)
}
```

### 3. Added Debugging Information

Added logging to help diagnose any issues:

```go
// Log the query and parameters for debugging
fmt.Printf("Executing query: %s with params: %s, %d\n", query, string(filtersJSON), depth)

// Log the result for debugging
fmt.Printf("Query result: %s\n", resultStr)
```

## Technical Explanation

The error occurred because GORM's `Scan()` method was trying to convert each character in the JSON string to a uint8 value (byte), but the conversion failed because the characters in the JSON string are not valid uint8 literals.

By changing the PostgreSQL function to return TEXT and scanning the result into a string variable, we avoid the type conversion issue. The string can then be converted to a byte slice using the `[]byte()` conversion, which is a safe operation in Go.

## Testing

After making these changes, the entity relationship graph query works correctly, and the "to a uint8: invalid syntax" error no longer occurs.
