package api

import (
	"encoding/json"
	"fmt"

	"github.com/crawler/db"
	"github.com/crawler/models"
)

// PostgresGraphService handles operations related to graph data using PostgreSQL functions
type PostgresGraphService struct{}

// NewPostgresGraphService creates a new PostgreSQL-based graph service
func NewPostgresGraphService() *PostgresGraphService {
	return &PostgresGraphService{}
}

// GetGraphData retrieves entities and relationships and converts them to GraphData using a PostgreSQL function
func (s *PostgresGraphService) GetGraphData(filters map[string]interface{}, depth int) (*models.GraphData, error) {
	// Convert filters to JSON
	filtersJSON, err := json.Marshal(filters)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal filters: %w", err)
	}

	// Special handling for null parent_id filter
	if parentID, ok := filters["parent_id"]; ok && parentID == nil {
		// Create a new map with the parent_id_is_null flag
		filtersMap := make(map[string]interface{})
		for k, v := range filters {
			if k != "parent_id" {
				filtersMap[k] = v
			}
		}
		filtersMap["parent_id_is_null"] = "true"

		// Re-marshal the updated filters
		filtersJSON, err = json.Marshal(filtersMap)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal filters with parent_id_is_null: %w", err)
		}
	}

	// Execute the PostgreSQL function
	var resultStr string
	query := `SELECT get_entity_graph($1, $2)`

	// Log the query and parameters for debugging
	fmt.Printf("Executing query: %s with params: %s, %d\n", query, string(filtersJSON), depth)

	err = db.DB.Raw(query, string(filtersJSON), depth).Scan(&resultStr).Error
	if err != nil {
		return nil, fmt.Errorf("failed to execute graph query: %w", err)
	}

	// Log the result for debugging
	fmt.Printf("Query result: %s\n", resultStr)

	// Parse the result
	var graphData models.GraphData
	if err := json.Unmarshal([]byte(resultStr), &graphData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal graph data: %w", err)
	}

	return &graphData, nil
}

// GetEntityWithRelationships retrieves a single entity with all its relationships
func (s *PostgresGraphService) GetEntityWithRelationships(entityID string) (*models.GraphData, error) {
	filters := map[string]interface{}{
		"id": entityID,
	}
	return s.GetGraphData(filters, 1) // Depth 1 to get immediate relationships
}

// GetEntitiesByType retrieves entities of a specific type with their relationships
func (s *PostgresGraphService) GetEntitiesByType(assetType string) (*models.GraphData, error) {
	filters := map[string]interface{}{
		"asset_type": assetType,
	}
	return s.GetGraphData(filters, 1) // Depth 1 to get immediate relationships
}

// GetRootEntities retrieves entities with no parent
func (s *PostgresGraphService) GetRootEntities() (*models.GraphData, error) {
	filters := map[string]interface{}{
		"parent_id": nil,
	}
	return s.GetGraphData(filters, 1) // Depth 1 to get immediate children
}
