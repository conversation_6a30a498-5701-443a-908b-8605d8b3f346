diff --git a/sqlite3-binding.c b/sqlite3-binding.c
index xxxxxxx..xxxxxxx 100644
--- a/sqlite3-binding.c
+++ b/sqlite3-binding.c
@@ -35898,7 +35898,7 @@
   { "mmap",        (sqlite3_syscall_ptr)mmap,         0  },
   { "munmap",      (sqlite3_syscall_ptr)munmap,       0  },
   { "mremap",      (sqlite3_syscall_ptr)mremap,       0  },
-  { "pread64",      (sqlite3_syscall_ptr)pread64,    0  },
+  { "pread",      (sqlite3_syscall_ptr)pread,    0  },
   { "pread",       (sqlite3_syscall_ptr)pread,        0  },
   { "pthread_mutex_alloc", (sqlite3_syscall_ptr)pthread_mutex_alloc, 0 },
   { "pthread_mutex_free",  (sqlite3_syscall_ptr)pthread_mutex_free,  0 },
@@ -35916,7 +35916,7 @@
   { "pthread_mutex_unlock",(sqlite3_syscall_ptr)pthread_mutex_unlock,0 },
   { "pthread_mutex_destroy",(sqlite3_syscall_ptr)pthread_mutex_destroy,0},
   { "pthread_cond_destroy",(sqlite3_syscall_ptr)pthread_cond_destroy,0},
-  { "pwrite64",     (sqlite3_syscall_ptr)pwrite64,   0  },
+  { "pwrite",     (sqlite3_syscall_ptr)pwrite,   0  },
   { "pwrite",      (sqlite3_syscall_ptr)pwrite,       0  },
   { "read",        (sqlite3_syscall_ptr)read,         0  },
   { "realloc",     (sqlite3_syscall_ptr)realloc,      0  },
@@ -35902,7 +35902,7 @@
 #define osMmap     ((void*(*)(void*,size_t,int,int,int,off_t))aSyscall[7].pCurrent)
 #define osMunmap   ((int(*)(void*,size_t))aSyscall[8].pCurrent)
 #define osMremap   ((void*(*)(void*,size_t,size_t,int))aSyscall[9].pCurrent)
-#define osPread64 ((ssize_t(*)(int,void*,size_t,off64_t))aSyscall[10].pCurrent)
+#define osPread64 ((ssize_t(*)(int,void*,size_t,off_t))aSyscall[10].pCurrent)
 #define osPread    ((ssize_t(*)(int,void*,size_t,off_t))aSyscall[11].pCurrent)
 #define osPthread_mutex_alloc ((int(*)(pthread_mutex_t**))aSyscall[12].pCurrent)
 #define osPthread_mutex_free  ((void(*)(pthread_mutex_t*))aSyscall[13].pCurrent)
@@ -35920,7 +35920,7 @@
 #define osPthread_mutex_unlock ((int(*)(pthread_mutex_t*))aSyscall[24].pCurrent)
 #define osPthread_mutex_destroy ((int(*)(pthread_mutex_t*))aSyscall[25].pCurrent)
 #define osPthread_cond_destroy ((int(*)(pthread_cond_t*))aSyscall[26].pCurrent)
-#define osPwrite64  ((ssize_t(*)(int,const void*,size_t,off64_t))\
+#define osPwrite64  ((ssize_t(*)(int,const void*,size_t,off_t))\
                                                  aSyscall[27].pCurrent)
 #define osPwrite   ((ssize_t(*)(int,const void*,size_t,off_t))aSyscall[28].pCurrent)
 #define osRead     ((ssize_t(*)(int,void*,size_t))aSyscall[29].pCurrent)
