# Patches for Dependencies

This directory contains patches for dependencies that may have compatibility issues on certain systems.

## go-sqlite3 Patch

The `sqlite3-fix.patch` file fixes compilation issues with the go-sqlite3 package on systems where 64-bit file offset functions (pread64, pwrite64, off64_t) are not available.

### Error Message

If you encounter the following error when building the application:

```
sqlite3-binding.c:35901:42: error: 'pread64' undeclared here (not in a function); did you mean 'pread'?
35901 |   { "pread64",      (sqlite3_syscall_ptr)pread64,    0  },
      |                                          ^~~~~~~ 
      |                                          pread
sqlite3-binding.c:35919:42: error: 'pwrite64' undeclared here (not in a function); did you mean 'pwrite'?
35919 |   { "pwrite64",     (sqlite3_syscall_ptr)pwrite64,   0  },
      |                                          ^~~~~~~~
      |                                          pwrite
sqlite3-binding.c: In function 'seekAndRead':
sqlite3-binding.c:35905:49: error: unknown type name 'off64_t'; did you mean 'off_t'?
```

This patch will fix the issue by replacing the 64-bit functions with their standard counterparts.

### How to Apply the Patch

#### Using Docker

The Dockerfiles in this project are already configured to apply the patch during the build process.

#### Manual Application

If you're building the application locally, you can apply the patch using the provided scripts:

On Linux/macOS:
```bash
chmod +x patches/apply-sqlite3-patch.sh
./patches/apply-sqlite3-patch.sh
```

On Windows:
```cmd
patches\apply-sqlite3-patch.bat
```

### What the Patch Does

The patch makes the following changes to the go-sqlite3 package:

1. Replaces `pread64` with `pread`
2. Replaces `pwrite64` with `pwrite`
3. Replaces `off64_t` with `off_t`

These changes allow the package to compile on systems where the 64-bit file offset functions are not available.
