package api

import (
	"net/http"

	"github.com/crawler/db"
	"github.com/crawler/models"
	"github.com/gin-gonic/gin"
)

// BlacklistHandler handles blacklist-related API endpoints
type BlacklistHandler struct {
	crawler CrawlerInterface
}

// NewBlacklistHandler creates a new blacklist handler
func NewBlacklistHandler(crawler CrawlerInterface) *BlacklistHandler {
	return &BlacklistHandler{
		crawler: crawler,
	}
}

// GetBlacklistedEntities returns all blacklisted entities
func (h *BlacklistHandler) GetBlacklistedEntities(c *gin.Context) {
	var entities []models.BlacklistedEntity
	query := db.DB

	// Apply filters if provided
	if assetType := c.Query("asset_type"); assetType != "" {
		query = query.Where("asset_type = ?", assetType)
	}

	if assetName := c.Query("asset_name"); assetName != "" {
		query = query.Where("asset_name LIKE ?", "%"+assetName+"%")
	}

	// Execute query
	if err := query.Find(&entities).Error; err != nil {
		c.J<PERSON>N(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, entities)
}

// AddBlacklistedEntity adds a new entity to the blacklist
func (h *BlacklistHandler) AddBlacklistedEntity(c *gin.Context) {
	var request struct {
		AssetType   string `json:"asset_type" binding:"required"`
		AssetName   string `json:"asset_name"`
		Pattern     string `json:"pattern"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create blacklisted entity
	entity := &models.BlacklistedEntity{
		AssetType:   request.AssetType,
		AssetName:   request.AssetName,
		Pattern:     request.Pattern,
		Description: request.Description,
	}

	// Check if entity already exists in blacklist
	var count int64
	db.DB.Model(&models.BlacklistedEntity{}).Where(
		"asset_type = ? AND asset_name = ?",
		entity.AssetType, entity.AssetName,
	).Count(&count)

	if count > 0 {
		c.JSON(http.StatusConflict, gin.H{"error": "Entity already blacklisted"})
		return
	}

	// Save to database
	if err := db.DB.Create(entity).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Refresh the crawler's blacklist cache
	h.crawler.RefreshBlacklistCache()

	c.JSON(http.StatusCreated, entity)
}

// RemoveBlacklistedEntity removes an entity from the blacklist
func (h *BlacklistHandler) RemoveBlacklistedEntity(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID is required"})
		return
	}

	// Check if entity exists
	var entity models.BlacklistedEntity
	if err := db.DB.First(&entity, "id = ?", id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Blacklisted entity not found"})
		return
	}

	// Remove from database
	if err := db.DB.Delete(&entity).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Refresh the crawler's blacklist cache
	h.crawler.RefreshBlacklistCache()

	c.JSON(http.StatusOK, gin.H{"message": "Entity removed from blacklist"})
}

// TestBlacklistPattern tests if an entity name matches a blacklist pattern
func (h *BlacklistHandler) TestBlacklistPattern(c *gin.Context) {
	var request struct {
		AssetType string `json:"asset_type" binding:"required"`
		AssetName string `json:"asset_name" binding:"required"`
		Pattern   string `json:"pattern" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create a temporary blacklist entity to test the pattern
	entity := &models.BlacklistedEntity{
		AssetType: request.AssetType,
		AssetName: "",
		Pattern:   request.Pattern,
	}

	// Test if the pattern matches
	matches := entity.MatchesPattern(request.AssetName)

	// Also test using SQL LIKE
	var count int64
	db.DB.Raw("SELECT 1 WHERE ? LIKE ?", request.AssetName, request.Pattern).Count(&count)
	sqlMatches := count > 0

	c.JSON(http.StatusOK, gin.H{
		"matches":     matches,
		"sql_matches": sqlMatches,
		"asset_type":  request.AssetType,
		"asset_name":  request.AssetName,
		"pattern":     request.Pattern,
	})
}
