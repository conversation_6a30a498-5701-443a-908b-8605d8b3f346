-- Improved function to delete an entity and all its children, applying preservation rules at all levels
CREATE OR REPLACE FUNCTION delete_entity_with_children(
    p_entity_id TEXT
)
RETURNS TABLE(deleted_count INT) AS $$
DECLARE
    v_count INT := 0;
    v_child RECORD;
    v_grandchild RECORD;
    v_preserved_count INT := 0;
    v_children_to_preserve TEXT[];
    v_grandchildren_to_process TEXT[];
BEGIN
    -- Check if entity exists
    IF NOT EXISTS (SELECT 1 FROM entities WHERE id = p_entity_id) THEN
        RETURN QUERY SELECT v_count;
        RETURN;
    END IF;

    -- First, identify all descendants that need to be preserved at any level
    -- This is a recursive process that builds a list of entities to preserve
    WITH RECURSIVE entity_tree AS (
        -- Start with direct children
        SELECT 
            id, 
            parent_id,
            ARRAY[id] AS path,
            1 AS level
        FROM entities
        WHERE parent_id = p_entity_id
        
        UNION ALL
        
        -- Add all descendants
        SELECT 
            e.id, 
            e.parent_id,
            et.path || e.id,
            et.level + 1
        FROM entities e
        JOIN entity_tree et ON e.parent_id = et.id
    ),
    -- Find all entities in the tree that have relationships with entities outside the tree
    entities_with_external_relations AS (
        SELECT DISTINCT et.id
        FROM entity_tree et
        JOIN entity_relationships er ON (er.from_entity_id = et.id OR er.to_entity_id = et.id)
        WHERE 
            -- The other entity in the relationship is not in the deletion tree
            (
                (er.from_entity_id != p_entity_id AND 
                 er.from_entity_id NOT IN (SELECT id FROM entity_tree) AND
                 er.from_entity_id IS NOT NULL)
                OR
                (er.to_entity_id != p_entity_id AND 
                 er.to_entity_id NOT IN (SELECT id FROM entity_tree) AND
                 er.to_entity_id IS NOT NULL)
            )
    ),
    -- Find all ancestors of entities with external relations
    -- These need to be preserved to maintain the path to the preserved entities
    ancestors_to_preserve AS (
        SELECT DISTINCT unnest(et.path) AS id
        FROM entity_tree et
        WHERE et.id IN (SELECT id FROM entities_with_external_relations)
    )
    -- Combine entities with external relations and their ancestors
    SELECT array_agg(DISTINCT id) INTO v_children_to_preserve
    FROM (
        SELECT id FROM entities_with_external_relations
        UNION
        SELECT id FROM ancestors_to_preserve
    ) AS entities_to_preserve;

    -- If we have entities to preserve, update their parent references
    IF v_children_to_preserve IS NOT NULL AND array_length(v_children_to_preserve, 1) > 0 THEN
        -- For direct children of the entity being deleted, set parent_id to NULL
        UPDATE entities
        SET parent_id = NULL
        WHERE parent_id = p_entity_id
        AND id = ANY(v_children_to_preserve);
        
        -- Count how many entities were preserved
        GET DIAGNOSTICS v_preserved_count = ROW_COUNT;
        
        -- Log for debugging
        RAISE NOTICE 'Preserved % direct children of entity %', v_preserved_count, p_entity_id;
    END IF;

    -- Process children that don't have relationships with other entities (depth-first approach)
    FOR v_child IN (
        SELECT id FROM entities 
        WHERE parent_id = p_entity_id
        AND (v_children_to_preserve IS NULL OR id <> ALL(v_children_to_preserve))
    ) LOOP
        -- Delete child recursively
        v_count := v_count + (SELECT * FROM delete_entity_with_children(v_child.id));
    END LOOP;

    -- Delete all relationships where this entity is involved
    DELETE FROM entity_relationships
    WHERE from_entity_id = p_entity_id OR to_entity_id = p_entity_id;

    -- Delete the entity
    DELETE FROM entities WHERE id = p_entity_id;
    
    -- Increment count for this entity
    v_count := v_count + 1;

    -- Return the total count of deleted entities
    RETURN QUERY SELECT v_count;
END;
$$ LANGUAGE plpgsql;
