# Nginx Configuration for Crawler System

This directory contains Nginx configuration files for the Crawler System.

## Overview

The Crawler System uses Nginx as a reverse proxy for both the frontend and backend services:

1. **Frontend Nginx (frontend/nginx.conf)**: Serves the React application and proxies API requests to the backend Nginx.
2. **Backend Nginx (nginx/api.conf)**: Proxies requests to the API service and adds CORS headers.

## Configuration Files

### Frontend Nginx Configuration (frontend/nginx.conf)

The frontend Nginx configuration:
- Serves static files from the React application
- Proxies API requests to the backend Nginx
- Handles SPA routing by redirecting to index.html
- Enables gzip compression
- Provides a health check endpoint

### Backend Nginx Configuration (nginx/api.conf)

The backend Nginx configuration:
- Proxies requests to the API service
- Adds CORS headers
- Handles preflight requests
- Enables gzip compression
- Provides a health check endpoint

## Docker Compose Setup

The Docker Compose configuration includes:

1. **frontend**: Nginx container that serves the React application
2. **nginx-api**: Nginx container that proxies requests to the API service
3. **api**: The API service that handles business logic

## Development vs Production

- **Development**: Use `docker-compose.override.yml` which exposes the API directly for debugging
- **Production**: Use `docker-compose.prod.yml` which adds restart policies and production-specific settings

## Running with Docker Compose

### Development

```bash
docker-compose up -d
```

### Production

```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## Customizing Nginx Configuration

To customize the Nginx configuration:

1. Edit the appropriate configuration file (`frontend/nginx.conf` or `nginx/api.conf`)
2. Rebuild and restart the containers:

```bash
docker-compose up -d --build frontend nginx-api
```
