# Blacklist Form Update

This document explains the changes made to the Asset Type dropdown in the Blacklist form.

## Changes Made

1. **Fixed Size Asset Type Dropdown**
   - Changed the Asset Type dropdown to have a fixed width of 300px
   - Centered the dropdown in the form
   - Added a maximum height to the dropdown menu to prevent it from being too tall
   - Added padding and font size styling to make the dropdown more readable

2. **Filter Dropdown Consistency**
   - Applied the same fixed width and styling to the filter dropdown in the main blacklist page
   - Centered the filter dropdown for better visual alignment
   - Added a maximum height to the dropdown menu

## Benefits

These changes provide several benefits:

1. **Consistent UI**: The fixed-width dropdowns create a more consistent and professional look
2. **Better Readability**: The centered, properly sized dropdowns are easier to read and use
3. **Improved User Experience**: The maximum height on dropdown menus prevents them from extending beyond the screen
4. **Visual Hierarchy**: The centered, prominent dropdowns emphasize the importance of selecting the correct asset type

## Implementation Details

The implementation uses:

- `Box` component with flex layout for centering
- Fixed width of 300px for both dropdowns
- `MenuProps` configuration to limit the dropdown menu height
- Custom styling for the select component to improve padding and font size

## Example

```jsx
<Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
  <FormControl required sx={{ width: 300 }}>
    <InputLabel>Asset Type</InputLabel>
    <Select
      value={newEntity.asset_type}
      onChange={(e) => setNewEntity({ ...newEntity, asset_type: e.target.value })}
      label="Asset Type"
      MenuProps={{
        PaperProps: {
          style: {
            maxHeight: 300
          }
        }
      }}
      sx={{ 
        '& .MuiSelect-select': { 
          padding: '12px 14px',
          fontSize: '1rem'
        }
      }}
    >
      {assetTypes.map((type) => (
        <MenuItem key={type} value={type}>
          {type}
        </MenuItem>
      ))}
    </Select>
  </FormControl>
</Box>
```
