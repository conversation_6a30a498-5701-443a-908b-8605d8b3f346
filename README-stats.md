# Entity Statistics Feature

This document explains the implementation of the Entity Statistics feature in the Crawler System.

## Overview

The Entity Statistics feature provides insights into the entities collected by the crawler, including:

- Total number of entities
- Distribution of entities by type
- Growth trends (entities created in the last 24 hours, week, and month)

## Implementation Details

### Backend

1. **Models**:
   - Added `EntityStats` struct in `models/stats.go` to represent entity statistics

2. **Services**:
   - Created `StatsService` in `api/stats_service.go` with methods to calculate entity statistics
   - Implemented database queries to gather statistics about entities

3. **API Endpoints**:
   - Added `/api/stats/entities` endpoint to retrieve entity statistics
   - Created `StatsHandler` in `api/stats_handlers.go` to handle statistics-related API requests

### Frontend

1. **API Service**:
   - Added `EntityStats` interface in `services/api.ts`
   - Implemented `getEntityStats()` method to fetch statistics from the API

2. **Statistics Page**:
   - Created `StatsPage.tsx` to display entity statistics
   - Implemented visualizations for entity type distribution
   - Added cards for summary statistics (total count, recent additions)

3. **Navigation**:
   - Added "Statistics" link to the main navigation drawer
   - Added "Statistics" link to the app bar
   - Added "View Statistics" button to the Dashboard's Quick Actions section

## Usage

The Statistics page provides several views:

1. **Summary Cards**: Quick overview of entity counts
   - Total entities
   - Entities created in the last 24 hours
   - Entities created in the last week
   - Entities created in the last month

2. **Type Distribution**: Breakdown of entities by type
   - Visual representation of the distribution
   - Count for each entity type

3. **Growth Trends**: Placeholder for future detailed growth analytics

## Future Enhancements

Potential future enhancements for the statistics feature:

1. **Time-based Graphs**: Show entity growth over time
2. **Relationship Statistics**: Analyze entity relationships
3. **Crawler Performance Metrics**: Track crawler efficiency
4. **Export Functionality**: Allow exporting statistics as CSV or PDF
5. **Custom Date Ranges**: Filter statistics by custom date ranges
