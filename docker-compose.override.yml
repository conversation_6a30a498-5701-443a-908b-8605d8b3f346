version: '3.8'

services:
  # Development-specific overrides
  
  # Add development-specific environment variables to the API service
  api:
    environment:
      - GIN_MODE=debug
    # For development, we can expose the API directly for debugging
    ports:
      - "${API_PORT:-8080}:${API_PORT:-8080}"
  
  # Add development-specific configuration to the frontend
  frontend:
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=/api
    
  # Add development-specific configuration to the nginx-api service
  nginx-api:
    volumes:
      - ./nginx/api.conf:/etc/nginx/conf.d/default.conf
    # Enable Nginx debug logging
    command: ["nginx-debug", "-g", "daemon off;"]
