import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  CircularProgress,
  Alert,
  Typography
} from '@mui/material';
import { useSnackbar } from 'notistack';
import apiService from '../services/api';

interface AddQueueItemDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  initialParentId?: string;
}

const AddQueueItemDialog: React.FC<AddQueueItemDialogProps> = ({ open, onClose, onSuccess, initialParentId = '' }) => {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [manualCrawlTypes, setManualCrawlTypes] = useState<string[]>([]);
  const [isManualOnly, setIsManualOnly] = useState(false);
  // Get template data based on data type
  const getTemplateData = (dataType: string): string => {
    switch (dataType) {
      case 'domain':
        return 'example.com';
      case 'ip':
        return '***********';
      case 'iprange':
        return '***********/24';
      case 'asn':
        return 'AS15169';
      case 'email':
        return '<EMAIL>';
      case 'org':
        return 'Example Organization';
      default:
        return '';
    }
  };

  const [newItem, setNewItem] = useState({
    data_type: 'domain',
    parent_id: initialParentId,
    data: getTemplateData('domain'),
    max_depth: 3 // Default max depth
  });

  // Fetch manual crawl types when the dialog opens
  useEffect(() => {
    if (open) {
      fetchManualCrawlTypes();
    }
  }, [open]);

  // Check if the selected data type is manual-only
  useEffect(() => {
    setIsManualOnly(manualCrawlTypes.includes(newItem.data_type));
  }, [newItem.data_type, manualCrawlTypes]);

  const fetchManualCrawlTypes = async () => {
    try {
      const response = await apiService.getManualCrawlTypes();
      setManualCrawlTypes(response.data.manual_crawl_types);
    } catch (error) {
      console.error('Error fetching manual crawl types:', error);
    }
  };

  const handleAddItem = async () => {
    try {
      // Check if data is empty
      if (!newItem.data.trim()) {
        enqueueSnackbar('Data cannot be empty', { variant: 'error' });
        return;
      }

      setLoading(true);
      console.log('Adding queue item:', newItem);

      // Create the payload
      const payload: {
        data_type: string;
        data: string;
        parent_id?: string;
        max_depth?: number;
      } = {
        data_type: newItem.data_type,
        data: newItem.data, // Send as string
        max_depth: newItem.max_depth
      };

      // Add parent_id only if it's not empty
      if (newItem.parent_id && newItem.parent_id.trim() !== '') {
        payload.parent_id = newItem.parent_id;
      }

      console.log('Sending payload:', payload);
      const response = await apiService.addQueueItem(payload);
      console.log('Response:', response);

      enqueueSnackbar('Queue item added successfully', { variant: 'success' });
      handleClose();

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error adding queue item:', error);
      enqueueSnackbar('Failed to add queue item', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    // Reset form state
    setNewItem({
      data_type: 'domain',
      parent_id: initialParentId,
      data: getTemplateData('domain'),
      max_depth: 3
    });
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>Add Queue Item</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required>
              <InputLabel>Data Type</InputLabel>
              <Select
                value={newItem.data_type}
                onChange={(e) => setNewItem({ ...newItem, data_type: e.target.value as string, data: getTemplateData(e.target.value as string) })}
                label="Data Type"
              >
                <MenuItem value="domain">Domain</MenuItem>
                <MenuItem value="ip">IP Address</MenuItem>
                <MenuItem value="iprange">IP Range</MenuItem>
                <MenuItem value="asn">ASN</MenuItem>
                <MenuItem value="email">Email</MenuItem>
                <MenuItem value="org">Organization</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Parent ID (Optional)"
              value={newItem.parent_id}
              onChange={(e) => setNewItem({ ...newItem, parent_id: e.target.value })}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              label="Max Depth"
              type="number"
              value={newItem.max_depth}
              onChange={(e) => {
                const value = parseInt(e.target.value);
                if (!isNaN(value) && value >= 0) {
                  setNewItem({ ...newItem, max_depth: value });
                }
              }}
              inputProps={{ min: 0 }}
              helperText="Default is 3"
            />
          </Grid>
          {isManualOnly && (
            <Grid item xs={12}>
              <Alert severity="info">
                <Typography variant="body2">
                  <strong>Note:</strong> This entity type is set to manual crawling only. When added to the queue:
                </Typography>
                <ul>
                  <li><Typography variant="body2">The entity will be created in the database but not crawled</Typography></li>
                  <li><Typography variant="body2">No children will be automatically discovered or crawled</Typography></li>
                  <li><Typography variant="body2">It will not be automatically requeued when its parent is requeued</Typography></li>
                  <li><Typography variant="body2">You must manually requeue it from the dashboard to fully crawl it</Typography></li>
                </ul>
              </Alert>
            </Grid>
          )}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Data (String)"
              value={newItem.data}
              onChange={(e) => setNewItem({ ...newItem, data: e.target.value })}
              multiline
              rows={10}
              required
              helperText="Enter the data as a plain string, not JSON"
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button
          onClick={handleAddItem}
          color="primary"
          variant="contained"
          disabled={!newItem.data_type || !newItem.data || loading}
        >
          {loading ? <CircularProgress size={24} /> : 'Add'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddQueueItemDialog;
