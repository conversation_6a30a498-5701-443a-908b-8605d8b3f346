FROM golang:1.19-alpine AS builder

# Install build dependencies for CGO
RUN apk add --no-cache gcc musl-dev sqlite-dev

WORKDIR /app

# Copy go.mod and go.sum
COPY go.mod go.sum* ./

# Download dependencies
RUN go mod download

# Fix go-sqlite3 compilation issues directly
RUN SQLITE3_DIR=$(go list -f '{{.Dir}}' github.com/mattn/go-sqlite3) && \
    if [ -n "$SQLITE3_DIR" ]; then \
        echo "Fixing go-sqlite3 package at: $SQLITE3_DIR" && \
        sed -i 's/{ "pread64",      (sqlite3_syscall_ptr)pread64,    0  },/{ "pread64",      (sqlite3_syscall_ptr)pread,    0  },/g' "$SQLITE3_DIR/sqlite3-binding.c" && \
        sed -i 's/{ "pwrite64",     (sqlite3_syscall_ptr)pwrite64,   0  },/{ "pwrite64",     (sqlite3_syscall_ptr)pwrite,   0  },/g' "$SQLITE3_DIR/sqlite3-binding.c" && \
        sed -i 's/off64_t/off_t/g' "$SQLITE3_DIR/sqlite3-binding.c" && \
        echo "Fixed go-sqlite3 package successfully"; \
    else \
        echo "Warning: go-sqlite3 package not found"; \
    fi

# Copy source code
COPY . .

# Build the application with CGO enabled
RUN CGO_ENABLED=1 GOOS=linux go build -o processor_service ./cmd/processor_service

# Create a minimal image
FROM alpine:latest

# Install runtime dependencies for CGO and SQLite
RUN apk add --no-cache ca-certificates libc6-compat sqlite-libs

WORKDIR /app

# Copy the binary from the builder stage
COPY --from=builder /app/processor_service .

# Create a directory for data files
RUN mkdir -p /app/data

# Copy any required data files
COPY --from=builder /app/data /app/data

# Expose the gRPC port
EXPOSE 50052

# Run the application
CMD ["./processor_service"]
