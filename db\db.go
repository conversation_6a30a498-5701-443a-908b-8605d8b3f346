package db

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/crawler/models"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	DB *gorm.DB
)

// Config holds database configuration
type Config struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
}

// Connect establishes a connection to the database
func Connect(config Config) (*gorm.DB, error) {
	dsn := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		config.Host, config.Port, config.User, config.Password, config.DBName, config.SSLMode,
	)

	// Configure GORM logger
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Set connection pool settings
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	// Set connection pool limits
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	DB = db
	return db, nil
}

// Migrate performs database migrations
func Migrate(db *gorm.DB) error {
	// First create the tables without foreign key constraints
	err := db.Migrator().CreateTable(
		&models.Entity{},
		&models.QueueItem{},
		&models.EntityRelationship{},
		&models.BlacklistedEntity{},
	)
	if err != nil {
		return err
	}

	// Then add the foreign key constraints
	err = db.AutoMigrate(
		&models.QueueItem{},
		&models.Entity{},
		&models.EntityRelationship{},
		&models.BlacklistedEntity{},
	)
	if err != nil {
		return err
	}

	// Execute SQL migrations for custom functions
	err = executeSQLMigrations(db)
	if err != nil {
		return err
	}

	return nil
}

// executeSQLMigrations executes SQL migration files
func executeSQLMigrations(db *gorm.DB) error {
	// Path to migrations directory
	migrationsDir := "db/migrations"

	// Check if directory exists
	if _, err := os.Stat(migrationsDir); os.IsNotExist(err) {
		log.Printf("Migrations directory not found: %s", migrationsDir)
		return nil // Not an error, just skip
	}

	// Read all SQL files in the migrations directory
	files, err := os.ReadDir(migrationsDir)
	if err != nil {
		return fmt.Errorf("failed to read migrations directory: %w", err)
	}

	// Execute each SQL file
	for _, file := range files {
		if filepath.Ext(file.Name()) != ".sql" {
			continue // Skip non-SQL files
		}

		filePath := filepath.Join(migrationsDir, file.Name())
		log.Printf("Executing SQL migration: %s", filePath)

		// Read the SQL file
		sqlBytes, err := os.ReadFile(filePath)
		if err != nil {
			return fmt.Errorf("failed to read SQL file %s: %w", filePath, err)
		}

		// Execute the SQL
		if err := db.Exec(string(sqlBytes)).Error; err != nil {
			return fmt.Errorf("failed to execute SQL file %s: %w", filePath, err)
		}

		log.Printf("Successfully executed SQL migration: %s", filePath)
	}

	return nil
}
