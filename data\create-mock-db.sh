#!/bin/bash

# Create a mock GeoLite2-ASN.db file for testing
echo "Creating mock GeoLite2-ASN.db file..."

# Check if sqlite3 is installed
if ! command -v sqlite3 &> /dev/null; then
    echo "Error: sqlite3 is not installed. Please install it first."
    exit 1
fi

# Create the database
sqlite3 GeoLite2-ASN.db <<EOF
CREATE TABLE asn_blocks (
  network TEXT,
  autonomous_system_number INTEGER,
  autonomous_system_organization TEXT
);

-- Add some sample data
INSERT INTO asn_blocks VALUES ('192.168.0.0/16', 64512, 'Example AS');
INSERT INTO asn_blocks VALUES ('10.0.0.0/8', 64513, 'Private Network AS');
INSERT INTO asn_blocks VALUES ('172.16.0.0/12', 64514, 'Internal AS');
INSERT INTO asn_blocks VALUES ('8.8.8.0/24', 15169, 'Google LLC');
INSERT INTO asn_blocks VALUES ('1.1.1.0/24', 13335, 'Cloudflare, Inc.');
EOF

echo "Mock GeoLite2-ASN.db created successfully."
