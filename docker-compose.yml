version: '3.8'

services:
  postgres:
    image: postgres:14-alpine
    container_name: crawler-postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-crawler}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 5s
      timeout: 5s
      retries: 5

  processor:
    build:
      context: .
      dockerfile: cmd/processor_service/Dockerfile
    container_name: crawler-processor
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      DB_HOST: ${DB_HOST:-postgres}
      DB_PORT: ${DB_PORT:-5432}
      DB_USER: ${POSTGRES_USER:-postgres}
      DB_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      DB_NAME: ${POSTGRES_DB:-crawler}
      DB_SSLMODE: ${DB_SSLMODE:-disable}
      GRPC_ADDR: :${PROCESSOR_GRPC_PORT:-50052}
    ports:
      - "${PROCESSOR_GRPC_PORT:-50052}:${PROCESSOR_GRPC_PORT:-50052}"

  crawler:
    build:
      context: .
      dockerfile: cmd/crawler_service/Dockerfile
    container_name: crawler-crawler
    depends_on:
      postgres:
        condition: service_healthy
      processor:
        condition: service_started
    environment:
      DB_HOST: ${DB_HOST:-postgres}
      DB_PORT: ${DB_PORT:-5432}
      DB_USER: ${POSTGRES_USER:-postgres}
      DB_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      DB_NAME: ${POSTGRES_DB:-crawler}
      DB_SSLMODE: ${DB_SSLMODE:-disable}
      GRPC_ADDR: :${CRAWLER_GRPC_PORT:-50051}
      WORKERS: ${WORKER_POOL:-5}
      MANUAL_CRAWL_ONLY: ${MANUAL_CRAWL_TYPES:-email,phone,social}
      PROCESSOR_SERVICE: grpc
      PROCESSOR_GRPC_ADDR: processor:${PROCESSOR_GRPC_PORT:-50052}
    ports:
      - "${CRAWLER_GRPC_PORT:-50051}:${CRAWLER_GRPC_PORT:-50051}"

  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: crawler-api
    depends_on:
      postgres:
        condition: service_healthy
      crawler:
        condition: service_started
      processor:
        condition: service_started
    environment:
      PORT: ${API_PORT:-8080}
      DB_HOST: ${DB_HOST:-postgres}
      DB_PORT: ${DB_PORT:-5432}
      DB_USER: ${POSTGRES_USER:-postgres}
      DB_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      DB_NAME: ${POSTGRES_DB:-crawler}
      DB_SSLMODE: ${DB_SSLMODE:-disable}
      CRAWLER_SERVICE: grpc
      CRAWLER_GRPC_ADDR: crawler:${CRAWLER_GRPC_PORT:-50051}
      PROCESSOR_SERVICE: grpc
      PROCESSOR_GRPC_ADDR: processor:${PROCESSOR_GRPC_PORT:-50052}
      AUTO_START_CRAWLER: ${AUTO_START_CRAWLER:-true}
    # No direct port exposure, accessed through nginx-api
    expose:
      - "${API_PORT:-8080}"

  nginx-api:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: crawler-nginx-api
    depends_on:
      - api
    ports:
      - "${API_PORT:-8080}:8080"

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.nginx.direct
    container_name: crawler-frontend
    depends_on:
      - nginx-api
    ports:
      - "80:80"
    environment:
      - REACT_APP_API_URL=${REACT_APP_API_URL:-/api}

volumes:
  postgres_data:
