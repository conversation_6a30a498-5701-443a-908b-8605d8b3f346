package shutdown

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/crawler/api"
)

// HandleSignals sets up signal handling for graceful shutdown
func HandleSignals(ctx context.Context, cancel context.CancelFunc, done chan struct{}, crawler api.CrawlerInterface) {
	// Create channel to listen for signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM, syscall.SIGHUP)

	// Wait for signal
	sig := <-sigChan
	log.Printf("Received shutdown signal: %v", sig)

	// Cancel the context to notify all operations to stop
	cancel()

	// Create a timeout context for shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// Stop the crawler if it's running
	log.Println("Stopping crawler...")
	
	// Create a channel to signal when crawler shutdown is complete
	crawlerDone := make(chan struct{})
	
	go func() {
		if err := crawler.Stop(); err != nil {
			log.Printf("Error stopping crawler: %v", err)
		}
		close(crawlerDone)
	}()

	// Wait for crawler to stop or timeout
	select {
	case <-crawlerDone:
		log.Println("Crawler stopped successfully")
	case <-shutdownCtx.Done():
		log.Println("Crawler shutdown timed out")
	}

	// Signal that shutdown is complete
	close(done)
}
