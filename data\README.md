# GeoLite2 Database Files

This directory should contain the GeoLite2 database files used by the application.

## Required Files

- `GeoLite2-ASN.db` - SQLite database for ASN lookups

## How to Obtain the GeoLite2 Database

The GeoLite2 databases are provided by MaxMind and require a license key to download. Follow these steps to obtain the database files:

1. Sign up for a MaxMind account at [https://www.maxmind.com/en/geolite2/signup](https://www.maxmind.com/en/geolite2/signup)
2. Generate a license key in your MaxMind account dashboard
3. Download the GeoLite2 ASN database in SQLite format
4. Place the downloaded `GeoLite2-ASN.db` file in this directory

## Alternative: Use a Mock Database

If you don't need actual geolocation data for development or testing, you can create a simple SQLite database with the required schema:

```bash
# Create a simple SQLite database
sqlite3 GeoLite2-ASN.db <<EOF
CREATE TABLE asn_blocks (
  network TEXT,
  autonomous_system_number INTEGER,
  autonomous_system_organization TEXT
);
INSERT INTO asn_blocks VALUES ('192.168.0.0/16', 64512, 'Example AS');
EOF
```

## Docker Configuration

The Docker containers are configured to use CGO_ENABLED=1 to support SQLite. The database files in this directory will be copied into the Docker containers during the build process.

## Troubleshooting

If you encounter the error:

```
invalid asndb: failed to ping sqlite database 'GeoLite2-ASN.db?_journal_mode=WAL&_busy_timeout=5000': Binary was compiled with 'CGO_ENABLED=0', go-sqlite3 requires cgo to work. This is a stub
```

It means that the application was compiled without CGO support. Make sure to build with `CGO_ENABLED=1` or use the provided Dockerfiles which have been configured correctly.
