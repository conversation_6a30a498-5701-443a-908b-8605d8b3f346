import React from 'react';
import './App.css';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { SnackbarProvider } from 'notistack';

import theme from './theme';
import Dashboard from './pages/Dashboard';
import EntitiesPage from './pages/EntitiesPage';
import QueuePage from './pages/QueuePage';
import GraphPage from './pages/GraphPage';
import SettingsPage from './pages/SettingsPage';
import BlacklistPage from './pages/BlacklistPage';
import StatsPage from './pages/StatsPage';

const App: React.FC = () => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <SnackbarProvider maxSnack={3} autoHideDuration={3000}>
        <Router>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/entities" element={<EntitiesPage />} />
            <Route path="/queue" element={<QueuePage />} />
            <Route path="/graph" element={<GraphPage />} />
            <Route path="/graph/entity/:id" element={<GraphPage />} />
            <Route path="/graph/type/:type" element={<GraphPage />} />
            <Route path="/graph/roots" element={<GraphPage />} />
            <Route path="/blacklist" element={<BlacklistPage />} />
            <Route path="/stats" element={<StatsPage />} />
            <Route path="/settings" element={<SettingsPage />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Router>
      </SnackbarProvider>
    </ThemeProvider>
  );
};

export default App;
