package api

import (
	"testing"

	"github.com/crawler/db"
	"github.com/crawler/models"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestDB(t *testing.T) *gorm.DB {
	// Create an in-memory SQLite database for testing
	testDB, err := gorm.Open(sqlite.Open("file::memory:?cache=shared"), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to connect to test database: %v", err)
	}

	// Migrate the schema
	err = testDB.AutoMigrate(&models.Entity{}, &models.EntityRelationship{})
	if err != nil {
		t.Fatalf("Failed to migrate test database: %v", err)
	}

	// Set the global DB variable to the test database
	db.DB = testDB

	return testDB
}

func TestDeleteEntityWithChildren(t *testing.T) {
	// Setup test database
	testDB := setupTestDB(t)

	// Create test entities
	// Root entity
	rootID := uuid.New().String()
	root := models.Entity{
		ID:        rootID,
		AssetType: "domain",
		AssetName: "example.com",
	}
	err := testDB.Create(&root).Error
	assert.NoError(t, err)

	// Parent entity
	parentID := uuid.New().String()
	parent := models.Entity{
		ID:        parentID,
		AssetType: "domain",
		AssetName: "parent.com",
	}
	err = testDB.Create(&parent).Error
	assert.NoError(t, err)

	// Set parent relationship for root entity
	root.ParentID = &parentID
	err = testDB.Save(&root).Error
	assert.NoError(t, err)

	// Create relationship between parent and root
	parentRootRel := models.EntityRelationship{
		FromEntityID: parentID,
		ToEntityID:   rootID,
		Label:        "has",
	}
	err = testDB.Create(&parentRootRel).Error
	assert.NoError(t, err)

	// Child entity
	childID := uuid.New().String()
	child := models.Entity{
		ID:        childID,
		AssetType: "subdomain",
		AssetName: "sub.example.com",
		ParentID:  &rootID,
	}
	err = testDB.Create(&child).Error
	assert.NoError(t, err)

	// Create relationship between root and child
	rootChildRel := models.EntityRelationship{
		FromEntityID: rootID,
		ToEntityID:   childID,
		Label:        "has",
	}
	err = testDB.Create(&rootChildRel).Error
	assert.NoError(t, err)

	// Grandchild entity
	grandchildID := uuid.New().String()
	grandchild := models.Entity{
		ID:        grandchildID,
		AssetType: "ip",
		AssetName: "***********",
		ParentID:  &childID,
	}
	err = testDB.Create(&grandchild).Error
	assert.NoError(t, err)

	// Create relationship between child and grandchild
	childGrandchildRel := models.EntityRelationship{
		FromEntityID: childID,
		ToEntityID:   grandchildID,
		Label:        "has",
	}
	err = testDB.Create(&childGrandchildRel).Error
	assert.NoError(t, err)

	// External entity (not part of the tree)
	externalID := uuid.New().String()
	external := models.Entity{
		ID:        externalID,
		AssetType: "domain",
		AssetName: "external.com",
	}
	err = testDB.Create(&external).Error
	assert.NoError(t, err)

	// Create relationship between external and grandchild
	externalGrandchildRel := models.EntityRelationship{
		FromEntityID: externalID,
		ToEntityID:   grandchildID,
		Label:        "references",
	}
	err = testDB.Create(&externalGrandchildRel).Error
	assert.NoError(t, err)

	// Verify initial state
	var initialCount int64
	testDB.Model(&models.Entity{}).Count(&initialCount)
	assert.Equal(t, int64(5), initialCount) // 5 entities: root, parent, child, grandchild, external

	var initialRelCount int64
	testDB.Model(&models.EntityRelationship{}).Count(&initialRelCount)
	assert.Equal(t, int64(4), initialRelCount) // 4 relationships

	// Create the service
	service := NewPostgresEntityService()

	// Execute the function to delete the root entity and its children
	deletedCount, err := service.DeleteEntityWithChildren(rootID)
	assert.NoError(t, err)
	assert.Equal(t, 2, deletedCount) // Root and child should be deleted, grandchild preserved due to external relationship

	// Verify that the root entity is deleted
	var rootEntity models.Entity
	err = testDB.First(&rootEntity, "id = ?", rootID).Error
	assert.Error(t, err) // Should not find the root entity

	// Verify that the child entity is deleted
	var childEntity models.Entity
	err = testDB.First(&childEntity, "id = ?", childID).Error
	assert.Error(t, err) // Should not find the child entity

	// Verify that the grandchild entity is preserved (due to external relationship)
	var grandchildEntity models.Entity
	err = testDB.First(&grandchildEntity, "id = ?", grandchildID).Error
	assert.NoError(t, err) // Should find the grandchild entity

	// Verify that the parent entity is preserved
	var parentEntity models.Entity
	err = testDB.First(&parentEntity, "id = ?", parentID).Error
	assert.NoError(t, err) // Should find the parent entity

	// Verify that the relationship between parent and root is deleted
	var parentRootRelationship models.EntityRelationship
	err = testDB.First(&parentRootRelationship, "from_entity_id = ? AND to_entity_id = ?", parentID, rootID).Error
	assert.Error(t, err) // Should not find the relationship

	// Verify that the relationship between external and grandchild is preserved
	var externalGrandchildRelationship models.EntityRelationship
	err = testDB.First(&externalGrandchildRelationship, "from_entity_id = ? AND to_entity_id = ?", externalID, grandchildID).Error
	assert.NoError(t, err) // Should find the relationship

	// Verify final counts
	var finalCount int64
	testDB.Model(&models.Entity{}).Count(&finalCount)
	assert.Equal(t, int64(3), finalCount) // 3 entities remaining: parent, grandchild, external

	var finalRelCount int64
	testDB.Model(&models.EntityRelationship{}).Count(&finalRelCount)
	assert.Equal(t, int64(1), finalRelCount) // Only 1 relationship remaining: external->grandchild
}
