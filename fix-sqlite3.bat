@echo off
setlocal enabledelayedexpansion

REM This script fixes compilation issues with the go-sqlite3 package
REM by replacing 64-bit file offset functions with their standard counterparts.

echo Fixing go-sqlite3 package...

REM Get the go-sqlite3 package directory
for /f "tokens=*" %%a in ('go list -f "{{.Dir}}" github.com/mattn/go-sqlite3') do set SQLITE3_DIR=%%a

if "!SQLITE3_DIR!" == "" (
    echo Error: go-sqlite3 package not found. Please run 'go get github.com/mattn/go-sqlite3' first.
    exit /b 1
)

echo Found go-sqlite3 package at: !SQLITE3_DIR!

REM Create temporary files for the replacements
echo Fixing pread64 function...
powershell -Command "(Get-Content '!SQLITE3_DIR!\sqlite3-binding.c') -replace '{ ""pread64"",      \(sqlite3_syscall_ptr\)pread64,    0  },', '{ ""pread64"",      (sqlite3_syscall_ptr)pread,    0  },' | Set-Content '!SQLITE3_DIR!\sqlite3-binding.c'"

echo Fixing pwrite64 function...
powershell -Command "(Get-Content '!SQLITE3_DIR!\sqlite3-binding.c') -replace '{ ""pwrite64"",     \(sqlite3_syscall_ptr\)pwrite64,   0  },', '{ ""pwrite64"",     (sqlite3_syscall_ptr)pwrite,   0  },' | Set-Content '!SQLITE3_DIR!\sqlite3-binding.c'"

echo Fixing off64_t type...
powershell -Command "(Get-Content '!SQLITE3_DIR!\sqlite3-binding.c') -replace 'off64_t', 'off_t' | Set-Content '!SQLITE3_DIR!\sqlite3-binding.c'"

echo Fixed go-sqlite3 package successfully!
echo Now you can build your application with CGO_ENABLED=1
