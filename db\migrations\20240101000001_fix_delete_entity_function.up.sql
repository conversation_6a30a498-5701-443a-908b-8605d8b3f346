-- Function to delete an entity and all its children, except for children that have relationships with other entities
CREATE OR REPLACE FUNCTION delete_entity_with_children(
    p_entity_id TEXT
)
RETURNS TABLE(deleted_count INT) AS $$
DECLARE
    v_count INT := 0;
    v_child RECORD;
    v_child_count INT;
    v_children_to_update TEXT[];
BEGIN
    -- Check if entity exists
    IF NOT EXISTS (SELECT 1 FROM entities WHERE id = p_entity_id) THEN
        RETURN QUERY SELECT v_count;
        RETURN;
    END IF;

    -- First, identify children that have relationships with other entities
    -- These will need their parent_id set to NULL instead of being deleted
    SELECT ARRAY_AGG(e.id) INTO v_children_to_update
    FROM entities e
    JOIN entity_relationships er ON (er.from_entity_id = e.id OR er.to_entity_id = e.id)
    WHERE e.parent_id = p_entity_id
    AND (
        (er.from_entity_id != p_entity_id AND er.to_entity_id != p_entity_id)
        OR
        (er.from_entity_id != p_entity_id OR er.to_entity_id != p_entity_id)
    );

    -- Update parent_id to NULL for children that have relationships with other entities
    -- This breaks the parent-child relationship without deleting the child
    IF v_children_to_update IS NOT NULL AND array_length(v_children_to_update, 1) > 0 THEN
        UPDATE entities
        SET parent_id = NULL
        WHERE id = ANY(v_children_to_update);
    END IF;

    -- Process children that don't have relationships with other entities (depth-first approach)
    FOR v_child IN (
        SELECT id FROM entities 
        WHERE parent_id = p_entity_id
        AND (v_children_to_update IS NULL OR id <> ALL(v_children_to_update))
    ) LOOP
        -- Delete child recursively
        v_count := v_count + (SELECT * FROM delete_entity_with_children(v_child.id));
    END LOOP;

    -- Delete all relationships where this entity is involved
    DELETE FROM entity_relationships
    WHERE from_entity_id = p_entity_id OR to_entity_id = p_entity_id;

    -- Delete the entity
    DELETE FROM entities WHERE id = p_entity_id;
    
    -- Increment count for this entity
    v_count := v_count + 1;

    -- Return the total count of deleted entities
    RETURN QUERY SELECT v_count;
END;
$$ LANGUAGE plpgsql;
