-- Fixed recursive function to delete an entity and all its children
-- Only preserves entities that have relationships with entities whose root parent
-- is different from the selected node
CREATE OR REPLACE FUNCTION delete_entity_with_children(
    p_entity_id TEXT
)
RETURNS TABLE(deleted_count INT, preserved_count INT) AS $$
DECLARE
    v_deleted_count INT := 0;
    v_preserved_count INT := 0;
    v_entities_to_preserve TEXT[];
BEGIN
    -- Check if entity exists
    IF NOT EXISTS (SELECT 1 FROM entities WHERE id = p_entity_id) THEN
        RETURN QUERY SELECT v_deleted_count, v_preserved_count;
        RETURN;
    END IF;

    -- First, build a complete entity tree starting from the target entity
    WITH RECURSIVE entity_tree AS (
        -- Start with the entity itself
        SELECT 
            id, 
            parent_id,
            ARRAY[id] AS path,
            0 AS level
        FROM entities
        WHERE id = p_entity_id
        
        UNION ALL
        
        -- Add all descendants
        SELECT 
            e.id, 
            e.parent_id,
            et.path || e.id,
            et.level + 1
        FROM entities e
        JOIN entity_tree et ON e.parent_id = et.id
    ),
    -- Get all entity IDs in the tree for easier reference
    tree_entities AS (
        SELECT id FROM entity_tree
    ),
    -- Find all relationships between entities in the tree and entities outside the tree
    external_relationships AS (
        SELECT 
            er.from_entity_id, 
            er.to_entity_id,
            CASE 
                WHEN er.from_entity_id IN (SELECT id FROM tree_entities) THEN er.from_entity_id
                ELSE er.to_entity_id
            END AS tree_entity_id,
            CASE 
                WHEN er.from_entity_id IN (SELECT id FROM tree_entities) THEN er.to_entity_id
                ELSE er.from_entity_id
            END AS external_entity_id
        FROM entity_relationships er
        WHERE 
            (er.from_entity_id IN (SELECT id FROM tree_entities) AND er.to_entity_id NOT IN (SELECT id FROM tree_entities))
            OR
            (er.to_entity_id IN (SELECT id FROM tree_entities) AND er.from_entity_id NOT IN (SELECT id FROM tree_entities))
    ),
    -- Find entities in our tree that have relationships with entities outside the tree
    entities_to_preserve AS (
        SELECT DISTINCT tree_entity_id
        FROM external_relationships
    )
    -- Get the list of entities to preserve
    SELECT array_agg(tree_entity_id) INTO v_entities_to_preserve
    FROM entities_to_preserve;

    -- Log for debugging
    RAISE NOTICE 'Entity % has % entities to preserve', p_entity_id, 
        CASE WHEN v_entities_to_preserve IS NULL THEN 0 
             ELSE array_length(v_entities_to_preserve, 1) 
        END;

    -- If the entity itself has external relationships, we can't delete it
    IF v_entities_to_preserve IS NOT NULL AND p_entity_id = ANY(v_entities_to_preserve) THEN
        RAISE NOTICE 'Entity % has external relationships and cannot be deleted', p_entity_id;
        RETURN QUERY SELECT v_deleted_count, v_preserved_count;
        RETURN;
    END IF;

    -- For direct children that need to be preserved, update their parent references
    UPDATE entities
    SET parent_id = NULL
    WHERE parent_id = p_entity_id
    AND (v_entities_to_preserve IS NOT NULL AND id = ANY(v_entities_to_preserve));
    
    -- Count how many direct children were preserved
    GET DIAGNOSTICS v_preserved_count = ROW_COUNT;
    RAISE NOTICE 'Preserved % direct children of entity %', v_preserved_count, p_entity_id;

    -- Now, recursively delete all children that don't have external relationships
    -- We'll use a recursive approach to delete from the bottom up
    WITH RECURSIVE entity_tree AS (
        -- Start with the entity itself
        SELECT 
            id, 
            parent_id,
            ARRAY[id] AS path,
            0 AS level
        FROM entities
        WHERE id = p_entity_id
        
        UNION ALL
        
        -- Add all descendants
        SELECT 
            e.id, 
            e.parent_id,
            et.path || e.id,
            et.level + 1
        FROM entities e
        JOIN entity_tree et ON e.parent_id = et.id
        -- Skip entities that need to be preserved
        WHERE (v_entities_to_preserve IS NULL OR e.id <> ALL(v_entities_to_preserve))
    ),
    -- Order entities by level (descending) to delete from the bottom up
    ordered_entities AS (
        SELECT id, level
        FROM entity_tree
        WHERE id != p_entity_id -- Don't include the root entity yet
        ORDER BY level DESC
    )
    -- Delete all relationships for these entities
    DELETE FROM entity_relationships
    WHERE from_entity_id IN (SELECT id FROM ordered_entities)
    OR to_entity_id IN (SELECT id FROM ordered_entities);

    -- Delete the entities themselves
    WITH ordered_entities AS (
        WITH RECURSIVE entity_tree AS (
            -- Start with the entity itself
            SELECT 
                id, 
                parent_id,
                ARRAY[id] AS path,
                0 AS level
            FROM entities
            WHERE id = p_entity_id
            
            UNION ALL
            
            -- Add all descendants
            SELECT 
                e.id, 
                e.parent_id,
                et.path || e.id,
                et.level + 1
            FROM entities e
            JOIN entity_tree et ON e.parent_id = et.id
            -- Skip entities that need to be preserved
            WHERE (v_entities_to_preserve IS NULL OR e.id <> ALL(v_entities_to_preserve))
        )
        SELECT id, level
        FROM entity_tree
        WHERE id != p_entity_id -- Don't include the root entity yet
        ORDER BY level DESC
    )
    DELETE FROM entities
    WHERE id IN (SELECT id FROM ordered_entities)
    RETURNING COUNT(*) INTO v_deleted_count;

    -- Finally, delete all relationships for the root entity
    DELETE FROM entity_relationships
    WHERE from_entity_id = p_entity_id OR to_entity_id = p_entity_id;

    -- Delete the root entity
    DELETE FROM entities WHERE id = p_entity_id;
    
    -- Increment count for the root entity
    v_deleted_count := v_deleted_count + 1;

    -- Return the total count of deleted and preserved entities
    RETURN QUERY SELECT v_deleted_count, v_preserved_count;
END;
$$ LANGUAGE plpgsql;
