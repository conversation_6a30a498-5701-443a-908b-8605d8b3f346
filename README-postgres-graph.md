# PostgreSQL-Based Entity Relationship Graph Query

This document explains the implementation of a PostgreSQL-based approach for retrieving entity relationship graph data, replacing the previous Golang implementation.

## Overview

The entity relationship graph query is a critical component of the crawler system that retrieves entities and their relationships for visualization. The previous implementation used Golang to recursively process entities and build the graph data, which could be inefficient for large datasets.

The new implementation uses a PostgreSQL function to perform the same operations directly in the database, which offers several advantages:

1. **Performance**: Database operations are more efficient for traversing relationships
2. **Reduced Network Traffic**: Only the final result is sent over the network
3. **Simplified Code**: Complex recursive logic is handled by the database
4. **Scalability**: PostgreSQL's query optimizer can better handle large datasets

## Implementation Details

### 1. PostgreSQL Function

A new PostgreSQL function `get_entity_graph` was created that:

- Takes filters and depth parameters as input
- Uses a recursive Common Table Expression (CTE) to traverse the entity graph
- Builds the nodes and links for the graph
- Returns the result as a JSON object

```sql
CREATE OR REPLACE FUNCTION get_entity_graph(
    p_filters JSONB,
    p_max_depth INT DEFAULT 1
)
RETURNS JSONB AS $$
DECLARE
    v_result JSONB;
BEGIN
    WITH RECURSIVE entity_graph AS (
        -- Base case: start with root entities based on filters
        SELECT 
            e.id,
            e.asset_type,
            e.asset_name,
            e.parent_id,
            e.attributes,
            0 AS depth
        FROM 
            entities e
        WHERE
            -- Filter conditions
            ...
        
        UNION
        
        -- Recursive case: get related entities up to max depth
        SELECT 
            e.id,
            e.asset_type,
            e.asset_name,
            e.parent_id,
            e.attributes,
            eg.depth + 1
        FROM 
            entities e
        JOIN 
            entity_graph eg ON (
                -- Relationship conditions
                ...
            )
        WHERE
            eg.depth < p_max_depth
    ),
    
    -- Get all nodes and links
    ...
    
    -- Build the final result
    SELECT 
        jsonb_build_object(
            'nodes', COALESCE(jsonb_agg(n.node_data), '[]'::jsonb),
            'links', COALESCE(..., '[]'::jsonb)
        ) INTO v_result
    FROM 
        nodes n;
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;
```

### 2. New Graph Service

A new `PostgresGraphService` was created to:

- Call the PostgreSQL function with the appropriate parameters
- Handle special cases like null parent_id filters
- Parse the JSON result into the expected GraphData structure

```go
// PostgresGraphService handles operations related to graph data using PostgreSQL functions
type PostgresGraphService struct{}

// GetGraphData retrieves entities and relationships using a PostgreSQL function
func (s *PostgresGraphService) GetGraphData(filters map[string]interface{}, depth int) (*models.GraphData, error) {
    // Convert filters to JSON
    filtersJSON, err := json.Marshal(filters)
    if err != nil {
        return nil, fmt.Errorf("failed to marshal filters: %w", err)
    }

    // Execute the PostgreSQL function
    var result []byte
    query := `SELECT get_entity_graph($1, $2)`
    err = db.DB.Raw(query, string(filtersJSON), depth).Scan(&result).Error
    if err != nil {
        return nil, fmt.Errorf("failed to execute graph query: %w", err)
    }

    // Parse the result
    var graphData models.GraphData
    if err := json.Unmarshal(result, &graphData); err != nil {
        return nil, fmt.Errorf("failed to unmarshal graph data: %w", err)
    }

    return &graphData, nil
}
```

### 3. Updated Graph Handler

The `GraphHandler` was updated to:

- Use an interface for the graph service to allow for different implementations
- Default to the PostgreSQL-based implementation for better performance

```go
// GraphServiceInterface defines the methods needed by the graph handler
type GraphServiceInterface interface {
    GetGraphData(filters map[string]interface{}, depth int) (*models.GraphData, error)
    GetEntityWithRelationships(entityID string) (*models.GraphData, error)
    GetEntitiesByType(assetType string) (*models.GraphData, error)
    GetRootEntities() (*models.GraphData, error)
}

// NewGraphHandler creates a new graph handler
func NewGraphHandler() *GraphHandler {
    // Use PostgreSQL-based graph service for better performance
    return &GraphHandler{
        graphService: NewPostgresGraphService(),
    }
}
```

### 4. Database Migration

A new migration system was added to:

- Execute SQL files in the `db/migrations` directory
- Create the PostgreSQL function during database initialization

## Benefits

1. **Improved Performance**: The PostgreSQL-based implementation is more efficient for large datasets
2. **Reduced Memory Usage**: The Golang implementation had to load all entities into memory
3. **Better Maintainability**: The SQL implementation is more concise and easier to understand
4. **Future Extensibility**: Additional optimizations can be made directly in the database

## Usage

The API endpoints remain the same, so no changes are needed in the frontend:

- `GET /api/graph`: Get graph data with optional filters and depth
- `GET /api/graph/entity/:id`: Get graph data for a specific entity
- `GET /api/graph/type/:type`: Get graph data for entities of a specific type
- `GET /api/graph/roots`: Get graph data for root entities (entities with no parent)

## Future Enhancements

1. **Pagination**: Add support for paginating large graph results
2. **Caching**: Implement caching for frequently accessed graphs
3. **Materialized Views**: Use PostgreSQL materialized views for common graph queries
4. **Performance Tuning**: Add indexes and optimize the SQL query based on usage patterns
