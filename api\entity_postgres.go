package api

import (
	"fmt"

	"github.com/crawler/db"
	"github.com/crawler/models"
)

// PostgresEntityService handles operations related to entities using PostgreSQL functions
type PostgresEntityService struct{}

// NewPostgresEntityService creates a new PostgreSQL-based entity service
func NewPostgresEntityService() *PostgresEntityService {
	return &PostgresEntityService{}
}

// DeleteEntityWithChildren deletes an entity and all its children,
// except for children that have relationships with entities whose root parent
// is different from the selected node, and preserves the direct parent of the root entity.
// This function properly handles foreign key constraints to avoid constraint violations.
func (s *PostgresEntityService) DeleteEntityWithChildren(entityID string) (int, error) {
	// Get the entity to delete for logging purposes
	var entity models.Entity
	if err := db.DB.First(&entity, "id = ?", entityID).Error; err != nil {
		return 0, fmt.Errorf("failed to find entity: %w", err)
	}

	// Count all descendants before deletion for statistics
	var totalDescendantCount int64
	query := `
		WITH RECURSIVE descendants AS (
			SELECT id FROM entities WHERE parent_id = ?
			UNION ALL
			SELECT e.id FROM entities e JOIN descendants d ON e.parent_id = d.id
		)
		SELECT COUNT(*) FROM descendants
	`
	if err := db.DB.Raw(query, entityID).Count(&totalDescendantCount).Error; err != nil {
		return 0, fmt.Errorf("failed to count descendants: %w", err)
	}

	// Log the entity being deleted
	fmt.Printf("Attempting to delete entity %s (Type: %s, Name: %s) with %d total descendants\n",
		entityID, entity.AssetType, entity.AssetName, totalDescendantCount)

	// Execute the PostgreSQL function
	var deletedCount, preservedCount int
	deleteQuery := `SELECT * FROM delete_entity_with_children($1)`

	// Use a transaction for better error handling
	tx := db.DB.Begin()
	if tx.Error != nil {
		return 0, fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}

	// Execute the function within the transaction
	type Result struct {
		DeletedCount   int
		PreservedCount int
	}
	var result Result
	err := tx.Raw(deleteQuery, entityID).Scan(&result).Error
	if err != nil {
		tx.Rollback()
		return 0, fmt.Errorf("failed to execute delete entity query: %w", err)
	}

	deletedCount = result.DeletedCount
	preservedCount = result.PreservedCount

	// Log the results
	fmt.Printf("Deleted entity %s with %d descendants. Preserved %d entities with external relationships.\n",
		entityID, deletedCount-1, preservedCount)

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return 0, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return deletedCount, nil
}
