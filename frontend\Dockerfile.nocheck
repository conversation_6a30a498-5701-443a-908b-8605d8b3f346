# Use Node.js as the base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy the rest of the code
COPY . .

# Set environment variables to disable TypeScript checking
ENV CI=false
ENV TSC_COMPILE_ON_ERROR=true
ENV ESLINT_NO_DEV_ERRORS=true
ENV DISABLE_ESLINT_PLUGIN=true
ENV SKIP_PREFLIGHT_CHECK=true

# Create a custom build script that skips TypeScript checking
RUN echo '#!/bin/sh\nCI=false DISABLE_ESLINT_PLUGIN=true TSC_COMPILE_ON_ERROR=true SKIP_PREFLIGHT_CHECK=true npm run build' > /app/build-without-checks.sh && \
    chmod +x /app/build-without-checks.sh

# Run the custom build script
RUN /app/build-without-checks.sh

# Create a directory to store the build output
RUN mkdir -p /output

# Copy the build output to the output directory
RUN cp -r build/* /output/

# Set the output directory as a volume
VOLUME /output

# Command to keep the container running
CMD ["tail", "-f", "/dev/null"]
