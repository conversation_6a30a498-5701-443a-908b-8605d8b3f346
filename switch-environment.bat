@echo off
setlocal enabledelayedexpansion

REM Script to switch between development and production environments

REM Display usage information
if "%~1"=="" (
  echo Usage: %0 [option]
  echo Options:
  echo   dev       - Use development environment (default^)
  echo   prod      - Use production environment
  echo   help      - Show this help message
  exit /b 1
)

REM Process the option
if "%~1"=="dev" (
  echo Switching to development environment...
  echo Stopping any running containers...
  docker-compose down

  echo Starting development environment...
  docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

  echo Development environment is now running.
  echo You can access the application at http://localhost
  echo The API is directly accessible at http://localhost:8080 for debugging
) else if "%~1"=="prod" (
  echo Switching to production environment...
  echo Stopping any running containers...
  docker-compose down

  echo Starting production environment...
  docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

  echo Production environment is now running.
  echo You can access the application at http://localhost
  echo The API is accessible through the frontend at http://localhost/api
) else if "%~1"=="help" (
  echo Usage: %0 [option]
  echo Options:
  echo   dev       - Use development environment (default^)
  echo   prod      - Use production environment
  echo   help      - Show this help message
  exit /b 0
) else (
  echo Error: Unknown option '%~1'
  echo Usage: %0 [option]
  echo Options:
  echo   dev       - Use development environment (default^)
  echo   prod      - Use production environment
  echo   help      - Show this help message
  exit /b 1
)
