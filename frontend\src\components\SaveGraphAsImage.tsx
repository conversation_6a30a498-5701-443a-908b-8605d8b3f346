import React from 'react';
import { <PERSON><PERSON>, Toolt<PERSON> } from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';

interface SaveGraphAsImageProps {
  graphRef: React.RefObject<any>;
  filename?: string;
  graphData?: any; // Optional graph data passed from parent component
}

// Function to escape XML special characters
const escapeXml = (unsafe: string): string => {
  if (unsafe === undefined || unsafe === null) {
    return '';
  }

  // Convert to string if it's not already
  const str = String(unsafe);

  // Replace all XML special characters
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;')
    // Replace any other potentially problematic characters
    .replace(/\r\n|\r|\n/g, ' ') // Replace newlines with spaces
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, ''); // Remove control characters
};

const SaveGraphAsImage: React.FC<SaveGraphAsImageProps> = ({
  graphRef,
  filename = 'entity-relationship-graph.svg',
  graphData: externalGraphData
}) => {
  const handleSaveImage = () => {
    if (!graphRef.current) {
      console.error('Graph reference is not available');
      return;
    }

    try {
      // First try to use the external graph data passed as a prop
      let graphData = externalGraphData;

      // If external data is not available, try to get it from the graph reference
      if (!graphData || !graphData.nodes || !graphData.links) {
        console.log('External graph data not available, trying to get it from the graph reference');
        const graph = graphRef.current;

        // Try different ways to access the graph data
        if (graph) {
          // Method 1: Try to access the graph data as a function
          if (typeof graph.graphData === 'function') {
            graphData = graph.graphData();
          }
          // Method 2: Try to access internal properties
          else if (graph._graphData) {
            graphData = graph._graphData;
          }
          // Method 3: Try to access __data property
          else if (graph.__data) {
            graphData = graph.__data;
          }
          // Method 4: Try to access graphData property directly
          else if (graph.graphData) {
            graphData = graph.graphData;
          }
        }
      }

      // Check if we have valid graph data
      if (!graphData || !graphData.nodes || !graphData.links) {
        console.error('Graph data not available from any source');
        if (graphRef.current) {
          console.log('Graph reference:', graphRef.current);
          console.log('Available properties:', Object.keys(graphRef.current));
        }

        // Fallback to capturing the canvas directly
        return saveCanvasDirectly();
      }

      console.log('Found graph data:', graphData);

      // Create an SVG representation of the graph
      const svgContent = generateSVG(graphData);

      // Create a Blob from the SVG content
      const blob = new Blob([svgContent], { type: 'image/svg+xml;charset=utf-8' });

      // Create a temporary link element
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename;

      // Append to body, click and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
      URL.revokeObjectURL(link.href);
    } catch (error) {
      console.error('Error saving graph as SVG:', error);
      // Fallback to capturing the canvas directly
      saveCanvasDirectly();
    }
  };

  // Fallback method to save the canvas directly as PNG
  const saveCanvasDirectly = () => {
    try {
      console.log('Falling back to direct canvas capture');

      // Find the canvas element
      let canvas = null;

      // Method 1: Try to find the canvas through the graph ref
      if (graphRef.current) {
        const graphElement = graphRef.current;

        // If graphRef.current is a DOM node, find canvas within it
        if (graphElement.querySelector) {
          canvas = graphElement.querySelector('canvas');
        }
        // If graphRef.current has a containerElem property
        else if (graphElement.containerElem) {
          canvas = graphElement.containerElem.querySelector('canvas');
        }
      }

      // Method 2: Look for canvas in the document
      if (!canvas) {
        // Try to find the canvas by ID
        canvas = document.querySelector('#entity-relationship-graph canvas');
      }

      // Method 3: Get all canvases and use the last one
      if (!canvas) {
        const canvases = document.querySelectorAll('canvas');
        if (canvases.length > 0) {
          canvas = canvases[canvases.length - 1];
        }
      }

      if (!canvas) {
        console.error('Canvas element not found');
        return;
      }

      // Convert canvas to PNG data URL
      const dataUrl = canvas.toDataURL('image/png');

      // Create a temporary link element
      const link = document.createElement('a');
      link.href = dataUrl;
      link.download = filename.replace('.svg', '.png');

      // Append to body, click and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error saving canvas directly:', error);
    }
  };

  // Function to generate SVG from graph data
  const generateSVG = (graphData: any) => {
    const { nodes, links } = graphData;
    const graph = graphRef.current;

    // Check if nodes have positions, if not, assign random positions
    const nodesWithPositions = nodes.map((node: any) => {
      if (node.x === undefined || node.y === undefined) {
        return {
          ...node,
          x: Math.random() * 1000,
          y: Math.random() * 1000
        };
      }
      return node;
    });

    // Get the current view parameters from the graph
    let currentScale = 1;
    let centerX = 0;
    let centerY = 0;

    // Try to get the current scale and center position from the graph
    if (graph) {
      // Different graph libraries might store these values differently
      if (graph.zoom) {
        // If zoom is a function, call it to get the current scale
        currentScale = typeof graph.zoom === 'function' ? graph.zoom() : graph.zoom;
      } else if (graph._zoom) {
        currentScale = graph._zoom;
      } else if (graph.scale) {
        currentScale = graph.scale;
      }

      // Get center coordinates
      if (graph.centerX && graph.centerY) {
        centerX = graph.centerX;
        centerY = graph.centerY;
      } else if (graph._centerX && graph._centerY) {
        centerX = graph._centerX;
        centerY = graph._centerY;
      }
    }

    // If we couldn't get the scale from the graph, calculate bounds from all nodes
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    // If we have center coordinates and scale, calculate the visible area
    if (currentScale !== 1 || (centerX !== 0 && centerY !== 0)) {
      // Get the canvas dimensions
      const canvasWidth = graph.width || window.innerWidth;
      const canvasHeight = graph.height || window.innerHeight;

      // Calculate the visible area based on the current view
      const visibleWidth = canvasWidth / currentScale;
      const visibleHeight = canvasHeight / currentScale;

      minX = centerX - visibleWidth / 2;
      minY = centerY - visibleHeight / 2;
      maxX = centerX + visibleWidth / 2;
      maxY = centerY + visibleHeight / 2;
    } else {
      // Fall back to calculating bounds from all nodes
      nodesWithPositions.forEach((node: any) => {
        if (node.x < minX) minX = node.x;
        if (node.y < minY) minY = node.y;
        if (node.x > maxX) maxX = node.x;
        if (node.y > maxY) maxY = node.y;
      });
    }

    // Add padding
    const padding = 50;
    minX -= padding;
    minY -= padding;
    maxX += padding;
    maxY += padding;

    const width = maxX - minX;
    const height = maxY - minY;

    // Start the SVG content
    // Use a proper XML declaration and SVG namespace
    let svg = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="${minX} ${minY} ${width} ${height}">
`;

    // Add a white background
    svg += `  <rect x="${minX}" y="${minY}" width="${width}" height="${height}" fill="white"/>
`;

    // Add the links (edges)
    links.forEach((link: any) => {
      // Handle both string IDs and object references in source/target
      const sourceId = typeof link.source === 'object' ? link.source.id : link.source;
      const targetId = typeof link.target === 'object' ? link.target.id : link.target;

      // Convert to string to ensure proper comparison
      const sourceIdStr = String(sourceId);
      const targetIdStr = String(targetId);

      const source = nodesWithPositions.find((node: any) => String(node.id) === sourceIdStr);
      const target = nodesWithPositions.find((node: any) => String(node.id) === targetIdStr);

      if (source && target && source.x !== undefined && source.y !== undefined && target.x !== undefined && target.y !== undefined) {
        // Determine link color
        const color = link.color || '#999999';

        // Draw the link
        svg += `  <line x1="${source.x}" y1="${source.y}" x2="${target.x}" y2="${target.y}" stroke="${color}" stroke-width="1.5"/>
`;

        // Add an arrow for directed graphs
        const dx = target.x - source.x;
        const dy = target.y - source.y;
        const angle = Math.atan2(dy, dx);

        // Arrow size
        const arrowLength = 10;

        // Calculate arrow points
        const arrowTip = { x: target.x - 15 * Math.cos(angle), y: target.y - 15 * Math.sin(angle) };
        const arrowLeft = {
          x: arrowTip.x - arrowLength * Math.cos(angle - Math.PI/6),
          y: arrowTip.y - arrowLength * Math.sin(angle - Math.PI/6)
        };
        const arrowRight = {
          x: arrowTip.x - arrowLength * Math.cos(angle + Math.PI/6),
          y: arrowTip.y - arrowLength * Math.sin(angle + Math.PI/6)
        };

        svg += `  <polygon points="${arrowTip.x},${arrowTip.y} ${arrowLeft.x},${arrowLeft.y} ${arrowRight.x},${arrowRight.y}" fill="${color}"/>
`;
      }
    });

    // Add the nodes
    nodesWithPositions.forEach((node: any) => {
      // Determine node color
      const color = node.color || '#1f77b4';

      // Draw the node
      svg += `  <circle cx="${node.x}" cy="${node.y}" r="5" fill="${color}" stroke="#ffffff" stroke-width="1.5"/>
`;

      // Add the node label
      let label = '';

      // Try to get the label from various properties
      if (node.name !== undefined && node.name !== null) {
        label = String(node.name);
      } else if (node.id !== undefined && node.id !== null) {
        label = String(node.id);
      } else {
        label = 'Unknown';
      }

      // Truncate long labels
      if (label.length > 30) {
        label = label.substring(0, 27) + '...';
      }

      // Escape special characters in the label
      const escapedLabel = escapeXml(label);

      // Add the text element with the escaped label
      svg += `  <text x="${node.x + 8}" y="${node.y + 4}" font-family="Arial" font-size="10" fill="#000000">${escapedLabel}</text>
`;
    });

    // Close the SVG
    svg += '</svg>';

    return svg;
  };

  return (
    <Tooltip title="Save current view as SVG image (fallback to PNG if SVG fails)">
      <Button
        variant="outlined"
        color="primary"
        startIcon={<SaveIcon />}
        onClick={handleSaveImage}
      >
        Save as Image
      </Button>
    </Tooltip>
  );
};

export default SaveGraphAsImage;
