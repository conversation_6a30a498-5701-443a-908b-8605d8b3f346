version: '3.8'

services:
  # Production-specific overrides
  
  # Add production-specific environment variables to the API service
  api:
    environment:
      - GIN_MODE=release
    restart: always
  
  # Add production-specific configuration to the frontend
  frontend:
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=/api
    restart: always
    
  # Add production-specific configuration to the nginx-api service
  nginx-api:
    restart: always
    
  # Add production-specific configuration to other services
  postgres:
    restart: always
    
  processor:
    restart: always
    
  crawler:
    restart: always
