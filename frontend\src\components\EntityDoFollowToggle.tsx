import React, { useState } from 'react';
import { Entity } from '../services/api';
import apiService from '../services/api';
import { useSnackbar } from 'notistack';
import { Box, FormControlLabel, Switch, Typography, Tooltip } from '@mui/material';

interface EntityDoFollowToggleProps {
  entity: Entity;
  onUpdate?: (updatedEntity: Entity) => void;
}

const EntityDoFollowToggle: React.FC<EntityDoFollowToggleProps> = ({ entity, onUpdate }) => {
  const [doFollow, setDoFollow] = useState<boolean>(entity.do_follow);
  const [loading, setLoading] = useState<boolean>(false);
  const { enqueueSnackbar } = useSnackbar();

  const handleToggle = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.checked;
    setLoading(true);
    
    try {
      const response = await apiService.updateEntityDoFollow(entity.id, newValue);
      setDoFollow(newValue);
      
      enqueueSnackbar(`Entity ${newValue ? 'will' : 'will not'} follow children for crawling`, { 
        variant: 'success',
        autoHideDuration: 3000
      });
      
      if (onUpdate && response.data.entity) {
        onUpdate(response.data.entity);
      }
    } catch (error) {
      console.error('Error updating entity do follow setting:', error);
      setDoFollow(entity.do_follow); // Reset to original value
      enqueueSnackbar('Failed to update entity setting', { 
        variant: 'error',
        autoHideDuration: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Tooltip title={
        doFollow 
          ? "When this entity is crawled, its children will also be crawled" 
          : "When this entity is crawled, its children will NOT be crawled"
      }>
        <FormControlLabel
          control={
            <Switch
              checked={doFollow}
              onChange={handleToggle}
              disabled={loading}
              color="primary"
            />
          }
          label={
            <Typography variant="body2">
              {doFollow ? "Do follow" : "Don't follow"}
            </Typography>
          }
        />
      </Tooltip>
    </Box>
  );
};

export default EntityDoFollowToggle;
