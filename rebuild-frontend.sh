#!/bin/bash

# Script to rebuild the frontend container

# Display usage information
function show_usage {
  echo "Usage: $0 [option]"
  echo "Options:"
  echo "  dev       - Rebuild frontend for development environment"
  echo "  prod      - Rebuild frontend for production environment"
  echo "  help      - Show this help message"
}

# Check if an option was provided
if [ $# -eq 0 ]; then
  show_usage
  exit 1
fi

# Process the option
case "$1" in
  dev)
    echo "Rebuilding frontend for development environment..."
    echo "Stopping frontend containers..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml stop frontend frontend-build
    
    echo "Rebuilding frontend containers..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml build frontend-build frontend
    
    echo "Starting frontend containers..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d frontend-build frontend
    
    echo "Frontend containers rebuilt and started for development environment."
    ;;
  prod)
    echo "Rebuilding frontend for production environment..."
    echo "Stopping frontend container..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml stop frontend
    
    echo "Rebuilding frontend container..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml build frontend
    
    echo "Starting frontend container..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d frontend
    
    echo "Frontend container rebuilt and started for production environment."
    ;;
  help)
    show_usage
    exit 0
    ;;
  *)
    echo "Error: Unknown option '$1'"
    show_usage
    exit 1
    ;;
esac
