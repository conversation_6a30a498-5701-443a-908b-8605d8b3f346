-- Function to delete an entity and all its children, except for children that have relationships with other entities
CREATE OR REPLACE FUNCTION delete_entity_with_children(
    p_entity_id TEXT
)
RETURNS TABLE(deleted_count INT) AS $$
DECLARE
    v_count INT := 0;
    v_child RECORD;
    v_child_count INT;
BEGIN
    -- Check if entity exists
    IF NOT EXISTS (SELECT 1 FROM entities WHERE id = p_entity_id) THEN
        RETURN QUERY SELECT v_count;
        RETURN;
    END IF;

    -- Process children first (depth-first approach)
    FOR v_child IN (
        SELECT id FROM entities WHERE parent_id = p_entity_id
    ) LOOP
        -- Check if child has relationships with other entities (not the parent)
        SELECT COUNT(*) INTO v_child_count
        FROM entity_relationships
        WHERE (from_entity_id = v_child.id OR to_entity_id = v_child.id)
        AND (from_entity_id != p_entity_id AND to_entity_id != p_entity_id);

        -- If child has no relationships with other entities, delete it recursively
        IF v_child_count = 0 THEN
            v_count := v_count + (SELECT * FROM delete_entity_with_children(v_child.id));
        END IF;
    END LOOP;

    -- Delete all relationships where this entity is involved
    DELETE FROM entity_relationships
    WHERE from_entity_id = p_entity_id OR to_entity_id = p_entity_id;

    -- Delete the entity
    DELETE FROM entities WHERE id = p_entity_id;
    
    -- Increment count for this entity
    v_count := v_count + 1;

    -- Return the total count of deleted entities
    RETURN QUERY SELECT v_count;
END;
$$ LANGUAGE plpgsql;
