#!/bin/bash

# Script to test API endpoints

# Base URL
BASE_URL="http://localhost"

# Test health endpoint
echo "Testing health endpoint..."
curl -v "${BASE_URL}/health"
echo ""
echo ""

# Test API health endpoint
echo "Testing API health endpoint..."
curl -v "${BASE_URL}/api/health"
echo ""
echo ""

# Test direct API health endpoint
echo "Testing direct API health endpoint..."
curl -v "http://localhost:8080/health"
echo ""
echo ""

# Test entities endpoint
echo "Testing entities endpoint..."
curl -v "${BASE_URL}/api/entities"
echo ""
echo ""

# Test queue endpoint
echo "Testing queue endpoint..."
curl -v "${BASE_URL}/api/queue"
echo ""
echo ""

# Test blacklist endpoint
echo "Testing blacklist endpoint..."
curl -v "${BASE_URL}/api/blacklist"
echo ""
echo ""

# Test graph endpoint
echo "Testing graph endpoint..."
curl -v "${BASE_URL}/api/graph"
echo ""
echo ""

# Test crawler endpoint
echo "Testing crawler endpoint..."
curl -v "${BASE_URL}/api/crawler/status"
echo ""
echo ""

echo "API tests completed."
