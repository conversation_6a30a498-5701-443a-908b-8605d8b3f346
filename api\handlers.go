package api

import (
	"errors"
	"fmt"
	"math"
	"net/http"
	"strconv"

	"github.com/crawler/crawler"
	"github.com/crawler/db"
	"github.com/crawler/models"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// <PERSON><PERSON> holds the API handlers
type Handler struct {
	crawler *crawler.Crawler
}

// NewHandler creates a new API handler
func <PERSON>Handler(crawler *crawler.Crawler) *Handler {
	return &Handler{
		crawler: crawler,
	}
}

// AddQueueItem adds a new item to the queue
func (h *Handler) AddQueueItem(c *gin.Context) {
	var request struct {
		ParentID *string     `json:"parent_id"`
		DataType string      `json:"data_type" binding:"required"`
		Data     interface{} `json:"data" binding:"required"`
		MaxDepth *int        `json:"max_depth"` // Optional max depth parameter
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create queue item
	item := &models.QueueItem{
		DataType: request.DataType,
		Status:   "pending",
		MaxDepth: 3, // Default max depth
	}

	// Set parent ID if provided
	if request.ParentID != nil {
		item.ParentID = request.ParentID
	}

	// Set max depth if provided
	if request.MaxDepth != nil {
		// Validate max depth (must be >= 0)
		if *request.MaxDepth < 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Max depth must be a non-negative integer"})
			return
		}

		// Add an upper limit to prevent excessive crawling
		const maxAllowedDepth = 100
		if *request.MaxDepth > maxAllowedDepth {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Max depth cannot exceed %d", maxAllowedDepth)})
			return
		}

		item.MaxDepth = *request.MaxDepth
	}

	// Set data
	if err := item.SetData(request.Data); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to set item data"})
		return
	}

	// Add to queue
	if err := h.crawler.AddToQueue(item); err != nil {
		// Check if this is a duplicate entity error
		if err.Error()[0:10] == "entity with" {
			// This is a duplicate entity, return a conflict status
			c.JSON(http.StatusConflict, gin.H{"error": err.Error(), "message": "Entity already exists"})
		} else {
			// Other error
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": item.ID})
}

// RemoveQueueItem removes an item from the queue
func (h *Handler) RemoveQueueItem(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID is required"})
		return
	}

	if err := h.crawler.RemoveFromQueue(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Item removed successfully"})
}

// StartProcessing starts the crawler
func (h *Handler) StartProcessing(c *gin.Context) {
	if err := h.crawler.Start(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Processing started"})
}

// StopProcessing stops the crawler
func (h *Handler) StopProcessing(c *gin.Context) {
	if err := h.crawler.Stop(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Processing stopped"})
}

// GetCrawlerStatus returns the current status of the crawler
func (h *Handler) GetCrawlerStatus(c *gin.Context) {
	status, message := h.crawler.GetStatus()
	c.JSON(http.StatusOK, gin.H{"status": status, "message": message})
}

// GetPendingItems returns pending items in the queue with pagination
func (h *Handler) GetPendingItems(c *gin.Context) {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 10 // Default limit
	}

	// Get paginated items
	items, totalCount, err := h.crawler.GetPendingItemsPaginated(page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(totalCount) / float64(limit)))
	if totalPages < 1 {
		totalPages = 1
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"data":         items,
		"total_count":  totalCount,
		"total_pages":  totalPages,
		"current_page": page,
		"limit":        limit,
	})
}

// GetProcessingItems returns items currently being processed with pagination
func (h *Handler) GetProcessingItems(c *gin.Context) {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 10 // Default limit
	}

	// Get paginated items
	items, totalCount, err := h.crawler.GetProcessingItemsPaginated(page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(totalCount) / float64(limit)))
	if totalPages < 1 {
		totalPages = 1
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"data":         items,
		"total_count":  totalCount,
		"total_pages":  totalPages,
		"current_page": page,
		"limit":        limit,
	})
}

// GetProcessedItems returns completed or failed items in the queue with pagination
func (h *Handler) GetProcessedItems(c *gin.Context) {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 10 // Default limit
	}

	// Get paginated items
	items, totalCount, err := h.crawler.GetProcessedItemsPaginated(page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(totalCount) / float64(limit)))
	if totalPages < 1 {
		totalPages = 1
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"data":         items,
		"total_count":  totalCount,
		"total_pages":  totalPages,
		"current_page": page,
		"limit":        limit,
	})
}

// RequeueEntity adds an existing entity back to the queue for reprocessing
// When requeuing, if the entity already exists, it will be crawled and updated
func (h *Handler) RequeueEntity(c *gin.Context) {
	entityID := c.Param("id")
	if entityID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Entity ID is required"})
		return
	}

	// Check if depth parameter is provided
	depthStr := c.Query("depth")
	var depth int = 3 // Default depth
	var err error

	if depthStr != "" {
		depth, err = strconv.Atoi(depthStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Depth must be a valid integer"})
			return
		}

		// Validate depth is non-negative
		if depth < 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Depth must be a non-negative integer"})
			return
		}

		// Add an upper limit to prevent excessive crawling
		const maxAllowedDepth = 100
		if depth > maxAllowedDepth {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Depth cannot exceed %d", maxAllowedDepth)})
			return
		}
	}

	// Requeue the entity with the specified depth
	if err := h.crawler.RequeueEntityWithDepth(entityID, depth); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Entity requeued successfully",
		"depth":   depth,
	})
}

// GetEntities returns entities based on filters with pagination
func (h *Handler) GetEntities(c *gin.Context) {
	var entities []models.Entity
	query := db.DB

	// Apply filters if provided
	if id := c.Query("id"); id != "" {
		query = query.Where("id = ?", id)
	}

	if assetType := c.Query("asset_type"); assetType != "" {
		query = query.Where("asset_type = ?", assetType)
	}

	if assetName := c.Query("asset_name"); assetName != "" {
		query = query.Where("asset_name LIKE ?", "%"+assetName+"%")
	}

	if parentID := c.Query("parent_id"); parentID != "" {
		query = query.Where("parent_id = ?", parentID)
	}

	// Get total count for pagination
	var totalCount int64
	if err := query.Model(&models.Entity{}).Count(&totalCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Error counting entities: %v", err)})
		return
	}

	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 10 // Default limit
	}

	offset := (page - 1) * limit

	// Apply pagination
	query = query.Limit(limit).Offset(offset)

	// Add ordering for consistent results
	query = query.Order("created_at DESC")

	// Execute query
	if err := query.Find(&entities).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(totalCount) / float64(limit)))
	if totalPages < 1 {
		totalPages = 1
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"data":         entities,
		"total_count":  totalCount,
		"total_pages":  totalPages,
		"current_page": page,
		"limit":        limit,
	})
}

// GetEntityRelationships returns entity relationships based on filters
func (h *Handler) GetEntityRelationships(c *gin.Context) {
	var relationships []models.EntityRelationship
	query := db.DB

	// Apply filters if provided
	if fromEntityID := c.Query("from_entity_id"); fromEntityID != "" {
		query = query.Where("from_entity_id = ?", fromEntityID)
	}

	if toEntityID := c.Query("to_entity_id"); toEntityID != "" {
		query = query.Where("to_entity_id = ?", toEntityID)
	}

	if label := c.Query("label"); label != "" {
		query = query.Where("label = ?", label)
	}

	// Execute query
	if err := query.Find(&relationships).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, relationships)
}

// GetManualCrawlTypes returns the list of entity types that should only be crawled manually
func (h *Handler) GetManualCrawlTypes(c *gin.Context) {
	// Get all registered processors
	manualCrawlTypes := []string{}

	// Check each data type to see if it's manual-only
	for _, dataType := range []string{"domain", "ip", "iprange", "asn", "email", "org"} {
		if h.crawler.IsManualCrawlOnly(dataType) {
			manualCrawlTypes = append(manualCrawlTypes, dataType)
		}
	}

	c.JSON(http.StatusOK, gin.H{"manual_crawl_types": manualCrawlTypes})
}

// UpdateEntityDoFollow updates the DoFollow flag for an entity
func (h *Handler) UpdateEntityDoFollow(c *gin.Context) {
	entityID := c.Param("id")
	if entityID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Entity ID is required"})
		return
	}

	// Parse request body
	var request struct {
		DoFollow bool `json:"do_follow" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Find the entity
	var entity models.Entity
	if err := db.DB.First(&entity, "id = ?", entityID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Entity not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	// Update the DoFollow flag
	entity.DoFollow = request.DoFollow

	// Save the entity
	if err := db.DB.Save(&entity).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to update entity: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Entity updated successfully",
		"entity":  entity,
	})
}

// DeleteEntityWithChildren deletes an entity and all its children,
// except for children that have relationships with entities whose root parent
// is different from the selected node, and preserves the direct parent of the root entity.
// This function properly handles foreign key constraints to avoid constraint violations.
func (h *Handler) DeleteEntityWithChildren(c *gin.Context) {
	entityID := c.Param("id")
	if entityID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Entity ID is required"})
		return
	}

	// Get the entity first to provide more detailed information in the response
	var entity models.Entity
	if err := db.DB.First(&entity, "id = ?", entityID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Entity not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	// Count all descendants before deletion for statistics
	var totalDescendantCount int64
	if err := db.DB.Raw(`
		WITH RECURSIVE descendants AS (
			SELECT id FROM entities WHERE parent_id = ?
			UNION ALL
			SELECT e.id FROM entities e JOIN descendants d ON e.parent_id = d.id
		)
		SELECT COUNT(*) FROM descendants
	`, entityID).Count(&totalDescendantCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to count descendants: %v", err)})
		return
	}

	// Count descendants with external relationships
	var externalRelationCount int64
	if err := db.DB.Raw(`
		WITH RECURSIVE descendants AS (
			SELECT id FROM entities WHERE parent_id = ?
			UNION ALL
			SELECT e.id FROM entities e JOIN descendants d ON e.parent_id = d.id
		)
		SELECT COUNT(DISTINCT d.id)
		FROM descendants d
		JOIN entity_relationships er ON (er.from_entity_id = d.id OR er.to_entity_id = d.id)
		WHERE (er.from_entity_id != ? AND er.to_entity_id != ?)
		AND (er.from_entity_id NOT IN (SELECT id FROM descendants) OR er.to_entity_id NOT IN (SELECT id FROM descendants))
	`, entityID, entityID, entityID).Count(&externalRelationCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to count external relationships: %v", err)})
		return
	}

	// Create entity service
	entityService := NewPostgresEntityService()

	// Delete the entity and its children
	deletedCount, err := entityService.DeleteEntityWithChildren(entityID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// If no entities were deleted, the entity probably doesn't exist
	if deletedCount == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Entity not found or could not be deleted due to relationships"})
		return
	}

	// Calculate preserved count
	preservedCount := int(totalDescendantCount) - (deletedCount - 1) // subtract 1 because deletedCount includes the entity itself
	if preservedCount < 0 {
		preservedCount = 0 // Handle edge cases
	}

	c.JSON(http.StatusOK, gin.H{
		"message":            "Entity and its children deleted successfully",
		"deleted_count":      deletedCount,
		"preserved_count":    preservedCount,
		"entity_type":        entity.AssetType,
		"entity_name":        entity.AssetName,
		"total_descendants":  totalDescendantCount,
		"external_relations": externalRelationCount,
	})
}
