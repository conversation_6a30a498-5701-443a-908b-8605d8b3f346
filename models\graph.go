package models

// GraphNode represents the structure needed for nodes in force-graph
type GraphNode struct {
	ID         string      `json:"id"`         // Must match Entity.ID
	Name       string      `json:"name"`       // Typically Entity.AssetName
	Type       string      `json:"type"`       // Entity.AssetType for coloring/grouping
	Attributes interface{} `json:"attributes"` // Entity.Attributes for additional data
}

// GraphLink represents the structure needed for links in force-graph
type GraphLink struct {
	Source   string `json:"source"`           // Source Entity ID (FromEntityID or ParentID)
	Target   string `json:"target"`           // Target Entity ID (ToEntityID or child ID)
	Label    string `json:"label,omitempty"`  // Relationship label
	Color    string `json:"color,omitempty"`  // Optional color from relationship
	LinkType string `json:"linkType,omitempty"` // e.g., "hierarchy", "cross-reference"
}

// GraphData is the final structure to be sent as JSON
type GraphData struct {
	Nodes []GraphNode `json:"nodes"`
	Links []GraphLink `json:"links"`
}
