#!/bin/bash

# Script to switch between different frontend build Dockerfiles

# Display usage information
function show_usage {
  echo "Usage: $0 [option]"
  echo "Options:"
  echo "  bypass     - Use Dockerfile.bypass (completely bypasses TypeScript checking)"
  echo "  nocheck    - Use Dockerfile.nocheck (skips TypeScript checking)"
  echo "  nots       - Use Dockerfile.nots (removes TypeScript from build process)"
  echo "  multistage - Use Dockerfile.multistage (multi-stage build with busybox)"
  echo "  fix-grid   - Use Dockerfile.fix-grid (fixes Grid component issues)"
  echo "  force      - Use Dockerfile.force (modifies package.json build script)"
  echo "  help       - Show this help message"
}

# Check if an option was provided
if [ $# -eq 0 ]; then
  show_usage
  exit 1
fi

# Process the option
case "$1" in
  bypass)
    DOCKERFILE="Dockerfile.bypass"
    ;;
  nocheck)
    DOCKERFILE="Dockerfile.nocheck"
    ;;
  nots)
    DOCKERFILE="Dockerfile.nots"
    ;;
  multistage)
    DOCKERFILE="Dockerfile.multistage"
    ;;
  fix-grid)
    DOCKERFILE="Dockerfile.fix-grid"
    ;;
  force)
    DOCKERFILE="Dockerfile.force"
    ;;
  help)
    show_usage
    exit 0
    ;;
  *)
    echo "Error: Unknown option '$1'"
    show_usage
    exit 1
    ;;
esac

# Update the docker-compose.yml file
echo "Switching frontend build to use $DOCKERFILE..."
sed -i "s/dockerfile: Dockerfile\.[a-z-]*/dockerfile: $DOCKERFILE/g" docker-compose.yml

echo "Done! The frontend build now uses $DOCKERFILE."
echo "To apply the changes, run: docker-compose up -d --build frontend-build"
